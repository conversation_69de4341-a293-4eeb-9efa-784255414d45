<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Match Game</title>
    <link rel="stylesheet" href="css/styles.css">
    <script src="js/badge-manager.js"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #DAA06D;
            --yarn-color: #FF7F50;
            --bg-color: #FAF3E0;
            --card-bg: #FFF8E8;
            --text-color: #333;
            --shadow-color: rgba(139, 69, 19, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--bg-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: rgba(255, 248, 232, 0.92);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.25);
            padding: 15px;
            text-align: center;
        }
        
        .title {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: var(--secondary-color);
            font-size: 1.2rem;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .game-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 20px;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .score-container {
            display: flex;
            gap: 20px;
        }
        
        .score-box {
            background-color: var(--card-bg);
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .difficulty-selector {
            display: flex;
            gap: 10px;
        }
        
        .difficulty-btn {
            padding: 8px 15px;
            background-color: var(--card-bg);
            border: 2px solid var(--secondary-color);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .difficulty-btn:hover, .difficulty-btn.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .game-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .card {
            aspect-ratio: 1 / 1;
            perspective: 1000px;
            cursor: pointer;
        }
        
        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        
        .card.flipped .card-inner {
            transform: rotateY(180deg);
        }
        
        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .card-front {
            background-color: var(--primary-color);
            color: white;
            font-size: 2rem;
        }
        
        .card-back {
            background-color: white;
            color: var(--primary-color);
            transform: rotateY(180deg);
            border: 2px solid var(--secondary-color);
            font-size: 0.9rem;
            padding: 5px;
            flex-direction: column;
        }
        
        .card-image {
            width: 80%;
            height: 70%;
            object-fit: contain;
            margin-bottom: 5px;
        }
        
        .card-name {
            font-weight: bold;
            text-align: center;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .control-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .control-btn:hover {
            background-color: var(--yarn-color);
            transform: translateY(-3px);
        }
        
        .control-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .instructions {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: var(--primary-color);
            font-style: italic;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: relative;
        }
        
        .close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        .result-title {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 15px;
        }
        
        .result-score {
            font-size: 3rem;
            font-weight: bold;
            color: var(--yarn-color);
            margin: 20px 0;
        }
        
        .result-message {
            margin-bottom: 20px;
        }
        
        .badge-display {
            margin: 20px 0;
            padding: 15px;
            background-color: var(--card-bg);
            border-radius: 10px;
            border: 2px dashed var(--secondary-color);
        }
        
        .badge-img {
            width: 100px;
            height: 100px;
            margin: 0 auto 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 3rem;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        #play-again {
            background-color: var(--primary-color);
            color: white;
        }
        
        #go-home {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .modal-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        /* Game description */
        .game-description {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .game-description h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        /* Back Button */
        .back-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            width: fit-content;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: var(--yarn-color);
            transform: translateY(-3px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        /* Hint styling */
        .card.hint {
            animation: hintPulse 1s;
        }
        
        @keyframes hintPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); box-shadow: 0 0 20px var(--yarn-color); }
            100% { transform: scale(1); }
        }
        
        /* For smaller screens */
        @media (max-width: 768px) {
            .game-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .game-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 576px) {
            .game-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .control-btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1 class="title">Memory Match Game</h1>
        <p class="subtitle">Match pairs to test your memory!</p>
    </header>
    
    <main class="main-container">
        <div class="game-description">
            <h2>Match Pairs to Win!</h2>
            <p>Flip cards to find matching pairs. Remember card positions and try to complete the game with as few moves as possible.</p>
        </div>
        
        <div class="game-container">
            <div class="game-header">
                <div class="score-container">
                    <div class="score-box">Moves: <span id="moves">0</span></div>
                    <div class="score-box">Matches: <span id="matches">0</span></div>
                </div>
                
                <div class="difficulty-selector">
                    <button class="difficulty-btn active" data-difficulty="easy">Easy</button>
                    <button class="difficulty-btn" data-difficulty="medium">Medium</button>
                    <button class="difficulty-btn" data-difficulty="hard">Hard</button>
                </div>
            </div>
            
            <div class="instructions" id="game-instructions">
                Click on cards to find matching pairs!
            </div>
            
            <div class="game-grid" id="game-grid">
                <!-- Cards will be generated here by JavaScript -->
            </div>
            
            <div class="controls">
                <button id="start-game" class="control-btn">Start New Game</button>
                <button id="hint-btn" class="control-btn">Show Hint</button>
            </div>
        </div>
        
        <a href="index.html" class="back-button">Back to Home</a>
    </main>
    
    <!-- Result Modal -->
    <div id="result-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 class="result-title">Congratulations!</h2>
            <p class="result-message">You've completed the game!</p>
            <div class="result-score">Moves: <span id="final-moves">0</span></div>
            
            <div id="badge-container" class="badge-display">
                <h3>Badge Earned!</h3>
                <div class="badge-img" id="badge-img">
                    <!-- Badge will be inserted here -->
                </div>
                <p id="badge-name">Memory Master</p>
            </div>
            
            <div class="modal-buttons">
                <button id="play-again" class="modal-btn">Play Again</button>
                <button id="go-home" class="modal-btn">Return to Home</button>
            </div>
        </div>
    </div>
    
    <script src="oncjqpx6dq.js"></script>
</body>
</html>