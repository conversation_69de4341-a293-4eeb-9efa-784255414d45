// Book narration enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Only run on book pages
    if (!document.getElementById('book')) return;
    
    // Initialize Agent Lee for book pages
    if (typeof initializeAgentLeeForBooks === 'function') {
        setTimeout(initializeAgentLeeForBooks, 1000);
    }
    
    // Set up voice recognition for book navigation
    setupVoiceRecognition();

    // Make sure pageFlip is accessible
    setTimeout(() => {
        // Find PageFlip instance
        if (typeof pageFlip !== 'undefined') {
            window.pageFlip = pageFlip;
        } else {
            // Try to find PageFlip in the window object
            const pageFlipKeys = Object.keys(window).filter(key => 
                key.startsWith('PageFlip') || 
                (window[key] && typeof window[key].turnToPage === 'function')
            );
            
            if (pageFlipKeys.length > 0) {
                window.pageFlip = window[pageFlipKeys[0]];
                console.log('Found PageFlip instance:', pageFlipKeys[0]);
            }
        }
    }, 1000);
    
    // Initialize variables
    const synth = window.speechSynthesis;
    window.currentUtterance = null;
    let autoNarrate = localStorage.getItem('auto-narration') === 'true';
    let femaleVoice = null;

    // Find female voice
    function loadVoices() {
        const voices = synth.getVoices();
        const femaleVoices = voices.filter(voice => 
            /en(-|_)/.test(voice.lang) && 
            (voice.name.includes('female') || 
             voice.name.includes('woman') || 
             voice.name.includes('girl') ||
             voice.name.includes('Samantha') ||
             voice.name.includes('Victoria') ||
             voice.name.includes('Karen'))
        );
        
        if (femaleVoices.length > 0) {
            femaleVoice = femaleVoices[0];
        }
    }

    if (synth.onvoiceschanged !== undefined) {
        synth.onvoiceschanged = loadVoices;
    }
    loadVoices();

    // Enhanced speak function for books - expose globally for Agent Lee
    window.speakPageContent = function() {
        // Stop any ongoing speech
        window.stopSpeaking();
        
        // Get current page
        if (!window.pageFlip) {
            console.log("PageFlip instance not found");
            return;
        }
        
        const currentPage = window.pageFlip.getCurrentPageIndex();
        const pageElement = document.querySelectorAll('.page')[currentPage];
        
        // Get page content
        let pageContent = '';
        
        if (pageElement) {
            // Get text content from the story-page or instruction-page div
            const contentDiv = pageElement.querySelector('.story-page') || pageElement.querySelector('.instruction-page');
            if (contentDiv) {
                // Get heading
                const heading = contentDiv.querySelector('h2');
                if (heading) {
                    pageContent += heading.textContent + '. ';
                }
                
                // Get paragraphs
                const paragraphs = contentDiv.querySelectorAll('p');
                paragraphs.forEach(p => {
                    pageContent += p.textContent + ' ';
                });
                
                // Get steps for instruction pages
                const steps = contentDiv.querySelectorAll('.instruction-step');
                steps.forEach(step => {
                    const stepNumber = step.querySelector('.step-number');
                    const stepContent = step.querySelector('.step-content');
                    
                    if (stepNumber && stepContent) {
                        pageContent += `Step ${stepNumber.textContent} ${stepContent.textContent} `;
                    }
                });
            } else {
                // Fallback to any text in the page
                pageContent = pageElement.textContent.trim();
            }
            
            // If still no content, use alt text from image
            if (!pageContent) {
                const img = pageElement.querySelector('img');
                if (img && img.alt) {
                    pageContent = `Page showing ${img.alt}`;
                }
            }
        }
        
        // If still no content, use generic message
        if (!pageContent) {
            pageContent = `You are viewing page ${currentPage + 1} of the book.`;
        }
        
        // Create utterance
        const utterance = new SpeechSynthesisUtterance(pageContent);
        
        // Set female voice if available
        if (femaleVoice) {
            utterance.voice = femaleVoice;
        }
        
        // Set rate from slider if available
        const speedSlider = document.getElementById('speed-slider');
        if (speedSlider) {
            utterance.rate = parseFloat(speedSlider.value);
        }
        
        // Visual feedback
        const speakBtn = document.getElementById('speak-btn');
        if (speakBtn) {
            speakBtn.classList.add('speaking');
        }
        
        // Events
        utterance.onend = function() {
            if (speakBtn) {
                speakBtn.classList.remove('speaking');
            }
            currentUtterance = null;
            
            // Auto-advance if it's enabled
            if (autoNarrate && pageFlip.getCurrentPageIndex() < pageFlip.getPageCount() - 1) {
                setTimeout(() => {
                    pageFlip.flipNext();
                    speakPageContent(); // Speak the next page
                }, 1000);
            }
        };
        
        // Store current utterance
        currentUtterance = utterance;
        
        // Speak
        synth.speak(utterance);
    }
    
    // Stop speaking function - expose globally for Agent Lee
    window.stopSpeaking = function() {
        if (synth.speaking) {
            synth.cancel();
            const speakBtn = document.getElementById('speak-btn');
            if (speakBtn) {
                speakBtn.classList.remove('speaking');
            }
            window.currentUtterance = null;
        }
    }
    
    // Toggle auto-narration
    function toggleAutoNarration() {
        autoNarrate = !autoNarrate;
        localStorage.setItem('auto-narration', autoNarrate);
        
        // Notify via Agent Lee if available
        const agentLeeChat = document.getElementById('chat-messages');
        if (agentLeeChat) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', 'agent-message');
            messageElement.textContent = autoNarrate ? 
                "Auto-narration enabled. I'll read each page and turn to the next one automatically." : 
                "Auto-narration disabled. I'll only read the current page when requested.";
            agentLeeChat.appendChild(messageElement);
            agentLeeChat.scrollTop = agentLeeChat.scrollHeight;
        }
        
        // Start narration if enabled
        if (autoNarrate) {
            speakPageContent();
        }
    }
    
    // Add narration controls to page if they don't exist
    if (!document.getElementById('auto-narration-toggle')) {
        // Find narration settings div
        const narrationSettings = document.querySelector('.narration-settings');
        
        if (narrationSettings) {
            // Add auto-narration toggle
            const autoNarrationLabel = document.createElement('label');
            autoNarrationLabel.setAttribute('for', 'auto-narration-toggle');
            autoNarrationLabel.textContent = 'Auto-Narration:';
            
            const autoNarrationToggle = document.createElement('input');
            autoNarrationToggle.setAttribute('type', 'checkbox');
            autoNarrationToggle.setAttribute('id', 'auto-narration-toggle');
            
            narrationSettings.appendChild(document.createElement('br'));
            narrationSettings.appendChild(autoNarrationLabel);
            narrationSettings.appendChild(autoNarrationToggle);
            
            // Set initial state
            autoNarrate = localStorage.getItem('auto-narration') === 'true';
            autoNarrationToggle.checked = autoNarrate;
            
            // Add event listener
            autoNarrationToggle.addEventListener('change', toggleAutoNarration);
        }
    }
    
    // Connect to existing book UI
    const speakBtn = document.getElementById('speak-btn');
    const stopBtn = document.getElementById('stop-btn');
    
    if (speakBtn) {
        // Override existing click handler
        speakBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent other handlers
            speakPageContent();
        });
    }
    
    if (stopBtn) {
        stopBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent other handlers
            stopSpeaking();
        });
    }
    
    // Initialize auto-narration if enabled
    if (autoNarrate) {
        setTimeout(() => {
            speakPageContent();
        }, 1500);
    }
    
    // Add Agent Lee narration integration
    window.bookSpeakPageContent = speakPageContent;
    window.bookStopSpeaking = stopSpeaking;
    window.bookToggleAutoNarration = toggleAutoNarration;
    
    // Setup voice recognition for book navigation
    function setupVoiceRecognition() {
        // Check if speech recognition is supported
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.log('Speech Recognition API not supported in this browser');
            return;
        }
        
        // Initialize speech recognition
        const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        // Handle results
        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript.toLowerCase();
            console.log('Voice command detected in book:', transcript);
            
            if (transcript.includes('agent lee') || transcript.includes('hey lee') || transcript.includes('hi lee')) {
                // Book reading commands
                if (transcript.includes('read this book') || transcript.includes('read the book') || transcript.includes('read to me')) {
                    const speakBtn = document.getElementById('speak-btn');
                    if (speakBtn) {
                        speakBtn.click();
                    }
                }
                // Navigation commands
                else if (transcript.includes('next page') || transcript.includes('turn page')) {
                    const nextBtn = document.getElementById('next-btn');
                    if (nextBtn) nextBtn.click();
                }
                else if (transcript.includes('previous page') || transcript.includes('go back')) {
                    const prevBtn = document.getElementById('prev-btn');
                    if (prevBtn) prevBtn.click();
                }
                else if (transcript.includes('stop reading') || transcript.includes('stop narration')) {
                    if (window.speechSynthesis) {
                        window.speechSynthesis.cancel();
                    }
                    
                    const stopBtn = document.getElementById('stop-btn');
                    if (stopBtn) stopBtn.click();
                }
                else if (transcript.includes('what is this about') || transcript.includes('tell me about this')) {
                    // Get book info
                    const title = document.querySelector('h1')?.textContent || 'this book';
                    const firstPageContent = document.querySelector('.page .story-page p, .page .instruction-page p')?.textContent || '';
                    
                    if (window.speakText) {
                        window.speakText(`This is ${title}. ${firstPageContent}`);
                    }
                }
            }
        };
        
        // Handle errors
        recognition.onerror = function(event) {
            console.error('Speech recognition error in book:', event.error);
        };
        
        // Restart listening after finishing
        recognition.onend = function() {
            // Only restart on mobile devices
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    try {
                        recognition.start();
                    } catch (e) {
                        console.log('Recognition already started in book view');
                    }
                }, 1000);
            }
        };
        
        // Start listening if on mobile
        if (window.innerWidth <= 768) {
            setTimeout(() => {
                try {
                    recognition.start();
                    console.log('Voice recognition started in book view');
                } catch (e) {
                    console.error('Failed to start voice recognition in book view:', e);
                }
            }, 2000);
        }
    }
});