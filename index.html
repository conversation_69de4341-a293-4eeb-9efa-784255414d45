<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>'s Library</title>
    <link rel="stylesheet" href="css/agent-lee.css">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #DAA06D;
            --yarn-color: #FF7F50;
            --bg-color: #FAF3E0;
            --card-bg: #FFF8E8;
            --text-color: #333;
            --shadow-color: rgba(139, 69, 19, 0.2);
            --footer-bg: #704214;
            --footer-text: #F4ECD8;
            --donate-highlight: #FF7F50;
        }
        
        /* Donation related styles */
        .donation-reminder {
            background-color: rgba(255, 127, 80, 0.1);
            border-left: 3px solid var(--donate-highlight);
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        
        .donation-reminder p {
            margin: 0;
            font-size: 1.1rem;
        }
        
        .donate-link {
            color: var(--donate-highlight);
            font-weight: bold;
            text-decoration: underline;
            transition: color 0.3s;
        }
        
        .donate-link:hover {
            color: #8B4513;
        }
        
        .support-banner {
            background-color: rgba(255, 248, 232, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
            border: 1px solid rgba(255, 127, 80, 0.3);
        }
        
        .support-banner h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .donate-button {
            display: inline-block;
            background-color: var(--donate-highlight);
            color: white;
            padding: 10px 20px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 15px;
            transition: transform 0.3s, background-color 0.3s;
        }
        
        .donate-button:hover {
            background-color: #FF6A3C;
            transform: translateY(-3px);
        }
        
        .highlight-donate {
            background-color: var(--donate-highlight) !important;
            color: white !important;
            animation: pulse-donate 2s infinite;
        }
        
        @keyframes pulse-donate {
            0% { box-shadow: 0 0 0 0 rgba(255, 127, 80, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 127, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 127, 80, 0); }
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', serif;
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            background-color: var(--bg-color);
            
        /* Donation Styles */
        .donation-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .donation-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 300px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .donation-card h3 {
            color: #8B4513;
            margin-bottom: 15px;
        }
        
        .donation-card img {
            width: 60px;
            height: 60px;
            margin-bottom: 15px;
        }
        
        .donation-form-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .donation-form-container h2 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .amount-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .amount-option {
            flex: 1;
            min-width: 70px;
        }
        
        .amount-option input[type="radio"] {
            display: none;
        }
        
        .amount-option label {
            display: block;
            padding: 10px;
            text-align: center;
            background-color: #f8f8f8;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .amount-option input[type="radio"]:checked + label {
            background-color: #FF7F50;
            color: white;
        }
        
        .stripe-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .stripe-button {
            background-color: #635bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            font-weight: bold;
            text-decoration: none;
            font-size: 16px;
            font-family: sans-serif;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stripe-button:hover {
            background-color: #524bb4;
        }
        
        .testimonials {
            margin-top: 40px;
        }
        
        .testimonials h2 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .testimonial-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        
        .testimonial {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .testimonial-text {
            font-style: italic;
            margin-bottom: 15px;
        }
        
        .testimonial-author {
            font-weight: bold;
            color: #8B4513;
            text-align: right;
        }
            background-image: 
                /* Watercolor texture overlay with more vibrant colors */
                url("data:image/svg+xml,%3Csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='watercolor' width='400' height='400' patternUnits='userSpaceOnUse' patternTransform='rotate(0)'%3E%3Cpath fill='%238B4513' fill-opacity='0.12' d='M0,200 C50,180 80,100 120,100 C140,100 180,140 200,180 C220,220 260,280 300,260 C340,240 360,200 400,200 L400,400 L0,400 Z'/%3E%3Cpath fill='%23DAA06D' fill-opacity='0.15' d='M0,150 C60,170 80,240 120,240 C180,240 220,150 300,150 C340,150 360,200 400,200 L400,0 L0,0 Z'/%3E%3Cpath fill='%23FF7F50' fill-opacity='0.12' d='M300,150 C320,120 350,100 380,100 C390,100 395,110 400,120 L400,0 L0,0 L0,100 C20,110 50,120 100,120 C150,120 200,150 250,150 C270,150 280,140 300,150 Z'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23watercolor)'/%3E%3C/svg%3E"),
                
                /* Textured fabric pattern for more depth */
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Cpath fill='%23FFC8A0' fill-opacity='0.08' d='M0,0 L40,0 L40,40 L0,40 Z M40,40 L80,40 L80,80 L40,80 Z M0,80 L40,80 L40,120 L0,120 Z M80,0 L120,0 L120,40 L80,40 Z M120,40 L160,40 L160,80 L120,80 Z M80,80 L120,80 L120,120 L80,120 Z M40,120 L80,120 L80,160 L40,160 Z M120,120 L160,120 L160,160 L120,160 Z M160,0 L200,0 L200,40 L160,40 Z M0,160 L40,160 L40,200 L0,200 Z M80,160 L120,160 L120,200 L80,200 Z M160,160 L200,160 L200,200 L160,200 Z'/%3E%3C/svg%3E"),
                
                /* Crochet pattern background with higher contrast */
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='600' viewBox='0 0 600 600'%3E%3Cpath fill='none' stroke='%238B4513' stroke-width='2.5' stroke-opacity='0.12' d='M0,0 C50,25 50,75 100,100 C150,125 150,175 200,200 C250,225 250,275 300,300 C350,325 350,375 400,400 C450,425 450,475 500,500 C550,525 550,575 600,600 M0,600 C50,575 50,525 100,500 C150,475 150,425 200,400 C250,375 250,325 300,300 C350,275 350,225 400,200 C450,175 450,125 500,100 C550,75 550,25 600,0'/%3E%3C/svg%3E"),
                
                /* Color splotches with higher opacity */
                radial-gradient(circle at 10% 20%, rgba(255, 127, 80, 0.18) 0%, transparent 25%),
                radial-gradient(circle at 90% 80%, rgba(218, 160, 109, 0.22) 0%, transparent 30%),
                radial-gradient(circle at 40% 60%, rgba(139, 69, 19, 0.15) 0%, transparent 35%),
                radial-gradient(circle at 70% 40%, rgba(255, 223, 186, 0.25) 0%, transparent 28%),
                radial-gradient(circle at 30% 90%, rgba(255, 179, 122, 0.17) 0%, transparent 32%),
                
                /* Warm gradient base with more vibrant end color */
                linear-gradient(135deg, var(--bg-color) 0%, #FFE9C9 100%);
            background-attachment: fixed;
            background-size: cover, 200px 200px, 600px 600px, auto, auto, auto, auto, auto, auto;
            background-position: center, center, center, center, center, center, center, center, center;
            background-repeat: no-repeat, repeat, repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat;
        }
        
        /* Adding a more vibrant yarn thread pattern that weaves through the page */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='800' viewBox='0 0 800 800'%3E%3Cpath fill='none' stroke='%23FF7F50' stroke-width='3' stroke-opacity='0.14' d='M0,400 C100,350 150,300 200,200 C250,100 300,50 400,50 C500,50 550,100 600,200 C650,300 700,350 800,400 C700,450 650,500 600,600 C550,700 500,750 400,750 C300,750 250,700 200,600 C150,500 100,450 0,400 Z'/%3E%3C/svg%3E"),
                         url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='800' viewBox='0 0 800 800'%3E%3Cpath fill='none' stroke='%23DAA06D' stroke-width='2' stroke-opacity='0.12' d='M800,200 C700,250 650,300 600,400 C550,500 500,550 400,550 C300,550 250,500 200,400 C150,300 100,250 0,200 C100,150 150,100 200,0 C250,-100 300,-150 400,-150 C500,-150 550,-100 600,0 C650,100 700,150 800,200 Z' transform='translate(0, 300)'/%3E%3C/svg%3E");
            background-size: cover, cover;
            background-position: center, center;
            pointer-events: none;
            z-index: -1;
            opacity: 0.9;
        }
        
        /* Header and Navigation */
        header {
            background-color: rgba(255, 248, 232, 0.92);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.25);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: 1px solid rgba(218, 160, 109, 0.3);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo img {
            height: 50px;
            margin-right: 10px;
        }
        
        .logo h1 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin: 0;
        }
        
        /* Main Navigation Styling */
        .main-navigation {
            display: flex;
            gap: 20px;
            padding: 10px 0;
            justify-content: center;
            margin: 0 auto;
            background-color: rgba(255, 248, 232, 0.85);
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.15);
        }
        
        .nav-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: bold;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
            font-size: 1rem;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--yarn-color);
            transition: width 0.3s;
        }
        
        .nav-link:hover {
            color: var(--yarn-color);
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .nav-link.active {
            color: var(--yarn-color);
            background-color: rgba(255, 127, 80, 0.1);
        }

        /* Content Section Styling */
        .content-section {
            display: none;
            padding: 20px;
            margin: 20px auto;
            max-width: 1200px;
            background-color: rgba(255, 248, 232, 0.9);
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(139, 69, 19, 0.25);
            animation: fadeIn 0.5s ease-in-out;
        }

        .content-section.active {
            display: block !important;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Additional styles will be added for sections */
        .section-title {
            color: var(--primary-color);
            font-size: 2.2rem;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(to right, transparent, var(--secondary-color), transparent);
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--primary-color);
            cursor: pointer;
        }
        
        /* Footer */
        footer {
            background-color: var(--footer-bg);
            color: var(--footer-text);
            padding: 50px 0 20px;
            position: relative;
            overflow: hidden;
            margin-top: 40px;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Cpath fill='%23FFFFFF' fill-opacity='0.07' d='M800,400 L600,200 L800,0 L800,400 Z M0,400 L200,200 L0,0 L0,400 Z'/%3E%3Cpath fill='none' stroke='%23FF7F50' stroke-width='3' stroke-opacity='0.09' d='M0,200 C400,350 400,50 800,200'/%3E%3C/svg%3E");
            pointer-events: none;
            opacity: 0.8;
            z-index: 0;
        }
        
        footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 8px;
            background: linear-gradient(to right, var(--yarn-color), var(--secondary-color), var(--yarn-color));
            opacity: 0.7;
        }
        
        .footer-content, .footer-bottom {
            position: relative;
            z-index: 1;
        }
        
        .footer-column h3 {
            position: relative;
            display: inline-block;
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--yarn-color);
            box-shadow: 0 1px 3px rgba(255, 127, 80, 0.3);
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            padding: 0 20px;
        }
        
        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: var(--yarn-color);
        }
        
        .footer-column p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .footer-column ul {
            list-style: none;
        }
        
        .footer-column ul li {
            margin-bottom: 10px;
        }
        
        .footer-column ul li a {
            color: var(--footer-text);
            text-decoration: none;
            transition: color 0.3s;
            display: inline-block;
        }
        
        .footer-column ul li a:hover {
            color: var(--yarn-color);
            transform: translateX(5px);
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            margin-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Responsiveness for navigation */
        @media (max-width: 768px) {
            .main-navigation {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .nav-link {
                padding: 5px 10px;
                font-size: 0.9rem;
            }
            
            .header-container {
                flex-wrap: wrap;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            nav.mobile-active {
                display: flex;
                flex-direction: column;
                width: 100%;
                padding: 10px;
            }
            
            nav.mobile-hidden {
                display: none;
            }
        }
        
        /* Hero Section Styles */
        .hero {
            text-align: center;
            padding: 60px 20px;
            background-color: rgba(255, 248, 232, 0.85);
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(139, 69, 19, 0.25);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
            border: 1px solid rgba(218, 160, 109, 0.3);
        }
        
        .hero h2 {
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9);
            position: relative;
            z-index: 2;
            color: var(--primary-color);
            font-size: 2.8rem;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .cta-button {
            display: inline-block;
            padding: 12px 30px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            margin: 10px;
        }
        
        .cta-button:hover {
            background-color: var(--yarn-color);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .cta-secondary {
            background-color: var(--secondary-color);
        }

        /* Resource Tables Styling */
        .resource-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0; /* Removed margin as it's on the container */
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
        }
        
        .resource-table th {
            background-color: var(--primary-color);
            color: white;
            text-align: left;
            padding: 12px 15px;
            white-space: nowrap; /* Prevent text wrapping */
        }
        
        .resource-table tr:nth-child(even) {
            background-color: rgba(218, 160, 109, 0.1);
        }
        
        .resource-table td {
            padding: 10px 15px;
            border-bottom: 1px solid rgba(139, 69, 19, 0.1);
            white-space: nowrap; /* Prevent text wrapping */
        }
        
        .resource-table tr:last-child td {
            border-bottom: none;
        }
        
        /* Wrap each table in a responsive container */
        .table-responsive {
            width: 100%;
            overflow-x: auto;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }
        
        /* Mobile-specific table styles */
        @media (max-width: 768px) {
            .resource-table th,
            .resource-table td {
                padding: 8px 10px;
                font-size: 0.9rem;
            }
            
            .resource-section h3 {
                font-size: 1.3rem;
            }
            
            .resource-section {
                padding: 0 5px;
            }
            
            .table-responsive {
                position: relative;
            }
            
            .table-responsive::after {
                content: "➡️";
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.5rem;
                opacity: 0.7;
                animation: bounce-right 1.5s infinite;
                pointer-events: none;
                display: block;
            }
            
            @keyframes bounce-right {
                0%, 100% { transform: translateY(-50%) translateX(0); }
                50% { transform: translateY(-50%) translateX(5px); }
            }
        }
        
        .resource-section {
            margin-bottom: 40px;
        }
        
        .resource-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .resource-section p {
            margin-bottom: 20px;
        }

        /* FAQ Styling */
        .faq-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .faq-item {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .faq-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(139, 69, 19, 0.25);
        }
        
        .faq-question {
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
            position: relative;
            padding-left: 25px;
        }
        
        .faq-question::before {
            content: '❓';
            position: absolute;
            left: 0;
            top: 0;
            color: var(--yarn-color);
        }
        
        .faq-answer {
            color: var(--text-color);
            display: none;
            padding-left: 25px;
            animation: fadeIn 0.5s ease;
        }
        
        .faq-item.active .faq-answer {
            display: block;
        }
        
        .faq-item.active {
            background-color: rgba(255, 248, 232, 0.9);
        }
        
        .faq-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .read-all-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .read-all-btn:hover {
            background-color: var(--yarn-color);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);
        }

        /* Book Cards Styling */
        .book-grid {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }
        
        .book-card {
            background-color: rgba(255, 248, 232, 0.92);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
            padding: 25px;
            width: 600px;
            max-width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(139, 69, 19, 0.2);
            display: flex;
            align-items: center;
            text-align: left;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
        }
        
        .book-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px var(--shadow-color);
        }
        
        .book-cover {
            flex: 0 0 250px;
            margin-right: 20px;
            cursor: pointer;
        }
        
        .book-cover img {
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.5s;
        }
        
        .book-card:hover .book-cover img {
            transform: scale(1.05);
        }
        
        .book-info {
            flex: 1;
        }
        
        .book-info h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            transition: color 0.3s;
            margin-bottom: 10px;
        }
        
        .book-card:hover .book-info h3 {
            color: var(--yarn-color);
        }
        
        .book-info p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .book-link {
            display: inline-block;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }
        
        .book-link:hover {
            background-color: var(--yarn-color);
        }
        
        @media (max-width: 768px) {
            .book-card {
                flex-direction: column;
                width: 90%;
                text-align: center;
            }
            
            .book-cover {
                flex: 0 0 auto;
                margin-right: 0;
                margin-bottom: 20px;
                width: 70%;
            }
            
            /* Ensure only one section is visible at a time */
            .content-section {
                display: none !important;
            }
            
            .content-section.active {
                display: block !important;
            }
        }

        /* About Section Styles */
        .about-author {
            display: flex;
            align-items: center;
            gap: 40px;
            margin-top: 20px;
        }
        
        .author-image {
            flex: 0 0 350px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .main-author-image {
            width: 100%;
            max-width: 350px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .author-image-gallery {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            width: 100%;
        }
        
        .thumbnail-image {
            width: 100%;
            height: 70px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .thumbnail-image:hover {
            transform: translateY(-3px);
            border-color: var(--yarn-color);
            box-shadow: 0 5px 15px rgba(255, 127, 80, 0.4);
        }

        /* Full screen image modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            animation: fadeIn 0.3s ease;
        }

        .image-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            margin: auto;
            animation: zoomIn 0.3s ease;
        }

        .modal-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 10px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .close-modal {
            position: absolute;
            top: -40px;
            right: 0px;
            color: #fff;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background-color: var(--yarn-color);
            transform: scale(1.1);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes zoomIn {
            from { 
                transform: scale(0.5);
                opacity: 0;
            }
            to { 
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Click hint for thumbnail images */
        .thumbnail-image::after {
            content: "🔍";
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .author-image-gallery {
            position: relative;
        }

        .author-image-gallery .thumbnail-image {
            position: relative;
        }

        .thumbnail-image:hover::after {
            opacity: 1;
        }
        
        .author-bio {
            flex: 1;
        }
        
        .author-bio h3 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 5px;
        }
        
        .author-bio h4 {
            color: var(--yarn-color);
            font-size: 1.2rem;
            margin-bottom: 20px;
            font-style: italic;
            font-weight: normal;
        }
        
        .author-bio p {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        .author-quote {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(139, 69, 19, 0.05);
            border-left: 4px solid var(--yarn-color);
            border-radius: 0 8px 8px 0;
        }
        
        .author-quote blockquote {
            font-style: italic;
            color: var(--primary-color);
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .author-quote cite {
            display: block;
            text-align: right;
            font-weight: bold;
            color: var(--yarn-color);
        }
        
        @media (max-width: 992px) {
            .about-author {
                flex-direction: column;
                text-align: center;
            }
            
            .author-image {
                flex: 0 0 auto;
                width: 250px;
            }
        }

        /* Contact Section */
        .contact-content {
            display: flex;
            gap: 40px;
        }
        
        .contact-info {
            flex: 1;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px var(--shadow-color);
            border: 1px solid rgba(218, 160, 109, 0.2);
        }
        
        .contact-info h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
            padding-bottom: 8px;
        }
        
        .contact-info h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, var(--primary-color), var(--yarn-color));
        }
        
        .contact-detail {
            margin-bottom: 20px;
            position: relative;
            padding-left: 50px;
        }
        
        .contact-icon {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background-color: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .inspiration-card {
            flex: 1;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px var(--shadow-color);
            border: 1px solid rgba(218, 160, 109, 0.2);
            background-color: rgba(255, 248, 232, 0.7);
        }
        
        .inspiration-card h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .inspiration-card blockquote {
            font-style: italic;
            color: var(--text-color);
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .quote-signature {
            text-align: right;
            color: var(--yarn-color);
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .contact-content {
                flex-direction: column;
            }
        }

        /* Settings Panel */
        .settings-panel {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
            display: none;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .settings-title {
            color: var(--primary-color);
            margin-top: 0;
        }
        
        .settings-option {
            margin: 10px 0;
        }

        /* Home Overview Styles */
        .home-overview {
            margin-bottom: 40px;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .overview-card {
            background-color: rgba(255, 248, 232, 0.9);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(218, 160, 109, 0.2);
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px var(--shadow-color);
        }

        .overview-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .overview-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .overview-card p {
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .overview-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            transition: background-color 0.3s ease;
        }

        .overview-link:hover {
            background-color: var(--yarn-color);
        }

        .featured-books-home {
            margin-bottom: 40px;
        }

        /* Video Embed Card Styles */
        .video-embed-card {
            background-color: rgba(255, 248, 232, 0.9);
            border-radius: 15px;
            border: 1px solid rgba(218, 160, 109, 0.2);
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s ease;
        }

        .video-embed-card:hover {
            transform: translateY(-5px);
        }

        .video-thumbnail {
            position: relative;
            cursor: pointer;
            margin-bottom: 15px;
            display: inline-block;
            max-width: 100%;
        }

        .video-thumbnail img {
            display: block;
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            border: 2px solid transparent;
            transition: border-color 0.3s ease;
        }

        .video-thumbnail:hover img {
            border-color: var(--yarn-color);
        }

        .play-button-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3rem;
            color: rgba(255, 255, 255, 0.8);
            background-color: rgba(0, 0, 0, 0.5);
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            transition: background-color 0.3s, color 0.3s;
        }

        .video-thumbnail:hover .play-button-overlay {
            background-color: rgba(255, 127, 80, 0.8);
            color: white;
        }

        .video-player-container {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            max-width: 100%;
            background: #000;
            display: none;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .video-player-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-embed-card h4 {
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .video-embed-card p {
            font-size: 1rem;
            color: var(--text-color);
            margin-bottom: 0;
            text-align: center;
        }

        .channel-card {
            background-color: rgba(255, 248, 232, 0.95);
        }

        .channel-button {
            display: inline-block;
            background-color: #e62117;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .channel-button:hover {
            background-color: #c41e3a;
        }

        /* Tutorials Section */
        .video-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .video-card {
            background-color: rgba(255, 248, 232, 0.9);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s;
            border: 1px solid rgba(218, 160, 109, 0.2);
        }
        
        .video-card:hover {
            transform: translateY(-10px);
        }
        
        .video-thumbnail {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            background-color: #f0f0f0;
            overflow: hidden;
        }
        
        .video-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .video-card:hover .video-thumbnail img {
            transform: scale(1.1);
        }
        
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background-color: rgba(255, 127, 80, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .play-button::after {
            content: '';
            display: block;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 15px solid white;
            margin-left: 5px;
        }
        
        .video-info {
            padding: 20px;
        }
        
        .video-info h3 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .video-info p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .video-meta {
            display: flex;
            justify-content: space-between;
            color: #888;
            font-size: 0.8rem;
        }

        /* Flipbook Modal Styles */
        .flipbook-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .flipbook-container {
            width: 100%;
            height: 100%;
            position: relative;
            background-color: rgba(0, 0, 0, 0.9);
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .close-flipbook {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.6);
            border: 2px solid white;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 1.8rem;
            cursor: pointer;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .close-flipbook:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        #book-cover-image {
            max-height: 100vh;
            max-width: 100vw;
            object-fit: contain;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        #book-cover-image:hover {
            transform: scale(1.02);
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Header with Navigation -->
    <header>
        <div class="header-container">
            <div class="logo">
                <img src="bmmq6xrkp3.png" alt="Leola's Library Logo" width="80">
            </div>
            
            <button class="mobile-menu-btn">☰</button>
        </div>
        
        <!-- Main Navigation -->
        <div class="header-container">
            <nav class="main-navigation">
                <a href="#home" class="nav-link active" data-section="home-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('home');">Home</a>
                <a href="#books" class="nav-link" data-section="books-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('books');">Books</a>
                <a href="#games" class="nav-link" data-section="games-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('games');">Games</a>
                <a href="#about" class="nav-link" data-section="about-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('about');">About Leola</a>
                <a href="#resources" class="nav-link" data-section="resources-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('resources');">Resources</a>
                <a href="#tutorials" class="nav-link" data-section="tutorials-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('tutorials');">Tutorials</a>
                <a href="#faq" class="nav-link" data-section="faq-page-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('faq');">FAQ</a>
                <a href="#contact" class="nav-link" data-section="contact-section" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('contact');">Contact</a>
                <a href="donations.html" class="nav-link highlight-donate" id="donate-link" onclick="if(window.speakAgentLeeSection) window.speakAgentLeeSection('donations');">Donate</a>
            </nav>
        </div>
    </header>

    <main>
        <!-- Home Section -->
        <div id="home-section" class="content-section active">
            <section class="hero">
                <img src="5bzyg72u33.png" alt="Leola's Crochet World" style="max-width: 100%; margin-bottom: 20px;">
                <h2>Welcome to Leola's Crochet World</h2>
                <p>Discover the art of crochet through interactive books, step-by-step tutorials, and a supportive community. From beginners to experienced crafters, there's something here for everyone who loves yarn crafts.</p>
                <div>
                    <a href="#books" class="cta-button" data-section="books-section">Explore Books</a>
                    <a href="#games" class="cta-button" data-section="games-section">Play Games</a>
                    <a href="#resources" class="cta-button cta-secondary" data-section="resources-section">Crochet Resources</a>
                </div>
            </section>

            <!-- Home Overview Section -->
            <section class="home-overview">
                <h2 class="section-title">What You'll Find Here</h2>
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="overview-icon">📚</div>
                        <h3>Interactive Books</h3>
                        <p>Dive into Sister Lee's heartwarming story "Needle & Yarn" and master crochet with the comprehensive "Crochet Mastery" guide.</p>
                        <a href="#books" class="overview-link" data-section="books-section">Explore Books</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">🎮</div>
                        <h3>Crochet Games</h3>
                        <p>Have fun while learning crochet with our interactive games designed to build your skills in an engaging way.</p>
                        <a href="#games" class="overview-link" data-section="games-section">Play Games</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">📋</div>
                        <h3>Crochet Resources</h3>
                        <p>Access our complete guides on yarn types, hook sizes, stitch patterns, and understanding crochet pattern structures.</p>
                        <a href="#resources" class="overview-link" data-section="resources-section">View Resources</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">📺</div>
                        <h3>Video Tutorials</h3>
                        <p>Learn with embedded video tutorials covering everything from beginner basics to advanced macrame-inspired techniques.</p>
                        <a href="#tutorials" class="overview-link" data-section="tutorials-section">Watch Tutorials</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">👩‍🏫</div>
                        <h3>About Sister Lee</h3>
                        <p>Meet Leola (Sister) Lee - artist, storyteller, and master crocheter who brings warmth and wisdom to every stitch.</p>
                        <a href="#about" class="overview-link" data-section="about-section">Meet Sister Lee</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">❓</div>
                        <h3>FAQ & Help</h3>
                        <p>Find answers to common crochet questions, troubleshooting tips, and guidance from our comprehensive FAQ section.</p>
                        <a href="#faq" class="overview-link" data-section="faq-page-section">Get Answers</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">📞</div>
                        <h3>Contact & Support</h3>
                        <p>Get in touch with Sister Lee, find inspiration, and connect with our crochet community.</p>
                        <a href="#contact" class="overview-link" data-section="contact-section">Get in Touch</a>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">💰</div>
                        <h3>Support Our Mission</h3>
                        <p>Your donations help us continue creating educational content and maintaining this site for the crochet community.</p>
                        <a href="donations.html" class="overview-link">Donate Now</a>
                    </div>
                </div>
            </section>

            <!-- Featured Books Section on Home Page -->
            <section class="featured-books-home">
                <h2 class="section-title">Featured Books</h2>
                <div class="book-grid">
                    <div class="book-card">
                        <div class="book-cover">
                            <img src="ruu3udr62d.png" alt="Needle & Yarn Cover">
                        </div>
                        <div class="book-info">
                            <h3>Needle & Yarn: A Love Stitched in Time</h3>
                            <p>A heartwarming tale of friendship and adventure, following the journey of Needle and Yarn as they create beautiful projects together.</p>
                            <a href="needle-yarn.html" class="book-link">Read Now</a>
                        </div>
                    </div>
                    
                    <div class="book-card">
                        <div class="book-cover">
                            <img src="jva31043ou.png" alt="Crochet Mastery Cover">
                        </div>
                        <div class="book-info">
                            <h3>Crochet Mastery: A Complete Guide</h3>
                            <p>Learn the art of crochet from beginner to expert with this comprehensive guide featuring step-by-step instructions.</p>
                            <a href="crochet-mastery.html" class="book-link">Learn Now</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- User Badges Section -->
            <section id="homepage-badges-section" class="badges-showcase">
                <h2 class="section-title">Your Earned Badges</h2>
                <div class="badges-container" id="homepage-badges-container">
                    <p class="no-badges" id="homepage-no-badges">You haven't earned any badges yet. Play games to collect badges!</p>
                    <!-- Badges will be displayed here -->
                </div>
            </section>
        </div>

        <!-- Books Section -->
        <div id="books-section" class="content-section">
            <h2 class="section-title">Interactive Flipbooks</h2>
            <div class="book-grid">
                <div class="book-card">
                    <div class="book-cover">
                        <img src="ruu3udr62d.png" alt="Needle & Yarn Cover">
                    </div>
                    <div class="book-info">
                        <h3>Needle & Yarn: A Love Stitched in Time</h3>
                        <p>A heartwarming tale of friendship and adventure, following the journey of Needle and Yarn as they create beautiful projects together.</p>
                        <a href="needle-yarn.html" class="book-link launch-book" data-book="needle-yarn">Open Book</a>
                    </div>
                </div>
                
                <div class="book-card">
                    <div class="book-cover">
                        <img src="jva31043ou.png" alt="Crochet Mastery Cover">
                    </div>
                    <div class="book-info">
                        <h3>Crochet Mastery: A Complete Guide</h3>
                        <p>Learn the art of crochet from beginner to expert with this comprehensive guide featuring step-by-step instructions.</p>
                        <a href="crochet-mastery.html" class="book-link launch-book" data-book="crochet-mastery">Open Book</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Games Section -->
        <div id="games-section" class="content-section">
            <h2 class="section-title">Interactive Crochet Games</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto 30px;">Have fun while learning crochet skills with these interactive games! Perfect for beginners or anyone looking to improve their techniques in an engaging way.</p>
            
            <div class="book-grid">
                <div class="book-card">
                    <div class="book-cover">
                        <img src="bhxud9qhuc.png" alt="Interactive Crochet Mastery Game">
                    </div>
                    <div class="book-info">
                        <h3>Interactive Crochet Simulator</h3>
                        <p>Build your crochet skills in this interactive 2D/3D simulator! Choose patterns, select yarns, and create beautiful projects in a virtual environment.</p>
                        <a href="games/crochet-simulator.html" class="book-link">Play Now</a>
                    </div>
                </div>
                
                <div class="book-card">
                    <div class="book-cover">
                        <img src="pqodhiogec.png" alt="Stitch Match Game">
                    </div>
                    <div class="book-info">
                        <h3>Stitch Match</h3>
                        <p>Test your knowledge of crochet stitches with this fun matching game. Perfect for beginners learning to identify different patterns!</p>
                        <a href="games/stitch-match.html" class="book-link">Play Now</a>
                    </div>
                </div>
            </div>
            
            <!-- Badges Display for Games Section -->
            <section class="badges-section" style="margin-top: 40px; background-color: white; border-radius: 10px; padding: 25px; box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);">
                <h2 style="color: #8B4513; font-size: 1.8rem; margin-bottom: 20px; text-align: center;">Your Earned Badges</h2>
                <div class="badges-grid" id="game-badges-container" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px;">
                    <p class="no-badges" id="no-game-badges" style="text-align: center; color: #666; font-style: italic; grid-column: 1 / -1; padding: 20px;">You haven't earned any badges yet. Play games to collect badges!</p>
                    <!-- Badges will be displayed here -->
                </div>
            </section>
        </div>

        <!-- About Section -->
        <div id="about-section" class="content-section">
            <h2 class="section-title">About Sister Lee</h2>
            <div class="about-author">
                <div class="author-image">
                    <img src="n5j7pqx39a.png" alt="Sister Lee in Glass" class="main-author-image">
                    <div class="author-image-gallery">
                        <img src="gdpjskd0z4.png" alt="Sister Lee outdoors" class="thumbnail-image">
                        <img src="0mvlpz8gmx.png" alt="Sister Lee smiling" class="thumbnail-image">
                        <img src="9ckfdwxdq0.png" alt="Sister Lee with coffee" class="thumbnail-image">
                        <img src="xhabe2hpi5.png" alt="Sister Lee in coat" class="thumbnail-image">
                    </div>
                </div>
                <div class="author-bio">
                    <h3>Leola (Sister) Lee</h3>
                    <h4>Artist. Storyteller. Matriarch. Teacher.</h4>
                    
                    <p>Born in the heart of Mississippi and a proud resident of Milwaukee since 1985, Leola (Sister) Lee is a woman whose life is stitched with purpose, creativity, and deep-rooted love. A mother of six, grandmother of thirteen, and great-grandmother of ten, she carries generations of care in her hands — whether she's crocheting a blanket, painting a portrait, or penning a story from the soul.</p>
                    
                    <p>A devout woman of faith, Sister Lee has been a pillar of her community for decades. Her artistry isn't confined to the yarn or canvas — it lives in the way she nurtures, uplifts, and teaches others. From local workshops to living rooms filled with grandkids, her gift has always been in guiding others with patience, warmth, and a deep belief that creativity heals.</p>
                    
                    <p>Her children's tale, <em>Needle & Yarn: A Love Story Stitched in Time</em>, reveals her storytelling heart. Through a charming, symbolic journey of connection and separation, Sister Lee explores themes of resilience, unity, and the invisible thread that ties people — and generations — together. The story echoes her own life, woven with trials, tenderness, and the triumph of love.</p>
                    
                    <p>In her companion work, <em>Crochet Mastery: A Complete Guide</em>, she transitions from storyteller to teacher — delivering a practical, step-by-step learning journey that makes the art of crochet accessible and joyful for everyone. The book is infused with her signature blend of encouragement and clarity, helping newcomers feel confident and inspired from their very first stitch.</p>
                    
                    <div class="author-quote">
                        <blockquote>"Through every page, Sister Lee's voice is clear: You are capable. You are creative. And you are not alone."</blockquote>
                        <cite>— About Leola (Sister) Lee</cite>
                    </div>
                </div>
            </div>
        </div>        <!-- Resources Section -->
        <div id="resources-section" class="content-section">
            <h2 class="section-title">Crochet Resources</h2>
            
            <!-- Mobile-friendly intro -->
            <div style="background-color: rgba(255, 248, 232, 0.92); border-radius: 15px; padding: 20px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);">
                <h3 style="color: var(--primary-color); margin-bottom: 15px; text-align: center;">Your Complete Crochet Reference</h3>
                <p style="margin-bottom: 15px;">Welcome to Sister Lee's curated crochet resources! Here you'll find all the reference information you need to make your crochet journey easier.</p>
                <p>From understanding pattern terminology to selecting the right yarn and hook, these guides will help you make confident choices for your projects.</p>
            </div>
            
            <!-- A. Crochet Patterns Info -->
            <div class="resource-section">
                <h3>Understanding Crochet Pattern Structures</h3>
                <p>This guide helps you understand how patterns are generally structured, listing common elements found in most crochet patterns. Familiarity with these components will help you confidently tackle any project.</p>
                
                <div class="table-responsive">
                    <table class="resource-table">
                    <thead>
                        <tr>
                            <th>Feature in a Pattern</th>
                            <th>Description</th>
                            <th>Why it's Important</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Skill Level</td>
                            <td>Beginner, Easy, Intermediate, Advanced</td>
                            <td>Helps choose projects matching your ability.</td>
                        </tr>
                        <tr>
                            <td>Materials List</td>
                            <td>Specific yarn, hook size, notions (scissors, markers, etc.)</td>
                            <td>Ensures you have everything needed.</td>
                        </tr>
                        <tr>
                            <td>Finished Size</td>
                            <td>Dimensions of the completed project.</td>
                            <td>Helps visualize the outcome.</td>
                        </tr>
                        <tr>
                            <td>Gauge</td>
                            <td>Stitches & rows per inch/cm.</td>
                            <td>CRUCIAL for correct sizing, especially for garments.</td>
                        </tr>
                        <tr>
                            <td>Abbreviations/Key</td>
                            <td>List of shorthand used (e.g., sc, dc).</td>
                            <td>Essential for decoding instructions.</td>
                        </tr>
                        <tr>
                            <td>Special Stitches</td>
                            <td>Instructions for any non-standard stitches used.</td>
                            <td>Learn unique techniques required for the pattern.</td>
                        </tr>
                        <tr>
                            <td>Notes</td>
                            <td>Important tips or clarifications from the designer.</td>
                            <td>Read these carefully before starting!</td>
                        </tr>
                        <tr>
                            <td>Instructions</td>
                            <td>Step-by-step guide, usually row-by-row or round-by-round.</td>
                            <td>The core of the pattern.</td>
                        </tr>
                        <tr>
                            <td>Finishing</td>
                            <td>Instructions for assembly, weaving ends, blocking.</td>
                            <td>Gives your project a polished look.</td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
            
            <!-- B. Yarn Guide -->
            <div class="resource-section">
                <h3>Leola's Yarn Guide</h3>
                <p>Understanding yarn types helps you select the perfect material for any project. This guide walks you through common yarn types, their characteristics, and best uses.</p>
                
                <div class="table-responsive">
                    <table class="resource-table">
                    <thead>
                        <tr>
                            <th>Yarn Name (Example)</th>
                            <th>Fiber Content</th>
                            <th>Weight</th>
                            <th>Rec. Hook</th>
                            <th>Texture/Feel</th>
                            <th>Best For</th>
                            <th>Care</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Lily Sugar'n Cream</td>
                            <td>100% Cotton</td>
                            <td>Worsted</td>
                            <td>4.5-5.5mm</td>
                            <td>Soft, sturdy</td>
                            <td>Dishcloths, bags</td>
                            <td>Machine wash</td>
                        </tr>
                        <tr>
                            <td>Red Heart Super Saver</td>
                            <td>100% Acrylic</td>
                            <td>Worsted</td>
                            <td>5.0-5.5mm</td>
                            <td>Durable, stiff</td>
                            <td>Afghans, amigurumi</td>
                            <td>Machine wash</td>
                        </tr>
                        <tr>
                            <td>Lion Brand Wool-Ease</td>
                            <td>80% Acrylic, 20% Wool</td>
                            <td>Worsted</td>
                            <td>5.0-5.5mm</td>
                            <td>Warm, soft</td>
                            <td>Sweaters, hats</td>
                            <td>Machine wash gentle</td>
                        </tr>
                        <tr>
                            <td>Patons Lace</td>
                            <td>80% Acrylic, 10% Mohair, 10% Wool</td>
                            <td>Lace</td>
                            <td>3.25-3.75mm</td>
                            <td>Light, airy</td>
                            <td>Shawls, delicate items</td>
                            <td>Hand wash</td>
                        </tr>
                        <tr>
                            <td>Bernat Blanket</td>
                            <td>100% Polyester</td>
                            <td>Super Bulky</td>
                            <td>8.0-9.0mm</td>
                            <td>Plush, thick</td>
                            <td>Quick blankets, chunky projects</td>
                            <td>Machine wash</td>
                        </tr>
                        <tr>
                            <td>Caron Simply Soft</td>
                            <td>100% Acrylic</td>
                            <td>Light Worsted</td>
                            <td>4.0-5.0mm</td>
                            <td>Silky, drapes well</td>
                            <td>Garments, baby items</td>
                            <td>Machine wash</td>
                        </tr>
                    </tbody>
                </table>
                </div>
                
                <h4 style="margin-top: 30px; margin-bottom: 15px; color: var(--primary-color);">Understanding Yarn Weights (0-7)</h4>
                <div class="table-responsive">
                    <table class="resource-table">
                    <thead>
                        <tr>
                            <th>Weight Category</th>
                            <th>Standard Number</th>
                            <th>Common Names</th>
                            <th>Typical Hook Size Range</th>
                            <th>Common Uses</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>0</td>
                            <td>Lace</td>
                            <td>Thread, Lace Weight</td>
                            <td>1.5-2.25mm</td>
                            <td>Doilies, fine lace, delicate edgings</td>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>Super Fine</td>
                            <td>Fingering, Sock, Baby</td>
                            <td>2.25-3.25mm</td>
                            <td>Socks, lightweight shawls, baby items</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Fine</td>
                            <td>Sport, Baby</td>
                            <td>3.25-3.75mm</td>
                            <td>Baby items, lightweight garments</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Light</td>
                            <td>DK, Light Worsted</td>
                            <td>3.75-4.5mm</td>
                            <td>Garments, accessories, lighter blankets</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>Medium</td>
                            <td>Worsted, Aran</td>
                            <td>4.5-5.5mm</td>
                            <td>Most common weight: blankets, garments, amigurumi</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>Bulky</td>
                            <td>Chunky, Craft</td>
                            <td>5.5-8mm</td>
                            <td>Quick projects, warm hats, scarves</td>
                        </tr>
                        <tr>
                            <td>6</td>
                            <td>Super Bulky</td>
                            <td>Roving, Super Chunky</td>
                            <td>8-12mm</td>
                            <td>Thick blankets, rugs, quick projects</td>
                        </tr>
                        <tr>
                            <td>7</td>
                            <td>Jumbo</td>
                            <td>Roving</td>
                            <td>12mm+</td>
                            <td>Extreme projects, arm crochet</td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
            
            <!-- C. Hook Sizes Guide -->
            <div class="resource-section">
                <h3>Crochet Hook Companion</h3>
                <p>Selecting the right hook size is crucial for achieving the correct gauge in your projects. This chart helps you understand hook sizing across different systems.</p>
                
                <div class="table-responsive">
                    <table class="resource-table">
                    <thead>
                        <tr>
                            <th>Hook Size (mm)</th>
                            <th>US Size</th>
                            <th>UK Size (approx)</th>
                            <th>Common Yarn Weight</th>
                            <th>Typical Projects</th>
                            <th>Material Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2.25</td>
                            <td>B-1</td>
                            <td>13</td>
                            <td>Lace, Fingering</td>
                            <td>Doilies, lace work</td>
                            <td>Steel for very fine work</td>
                        </tr>
                        <tr>
                            <td>3.25</td>
                            <td>D-3</td>
                            <td>10</td>
                            <td>Sport, Fingering</td>
                            <td>Baby items, socks</td>
                            <td>Aluminum, plastic</td>
                        </tr>
                        <tr>
                            <td>4.00</td>
                            <td>G-6</td>
                            <td>8</td>
                            <td>DK, Light Worsted</td>
                            <td>Garments, scarves</td>
                            <td>Aluminum, bamboo</td>
                        </tr>
                        <tr>
                            <td>5.00</td>
                            <td>H-8</td>
                            <td>6</td>
                            <td>Worsted</td>
                            <td>Blankets, hats</td>
                            <td>Most common hook size</td>
                        </tr>
                        <tr>
                            <td>5.50</td>
                            <td>I-9</td>
                            <td>5</td>
                            <td>Worsted, Aran</td>
                            <td>Sweaters, amigurumi</td>
                            <td>Good all-purpose size</td>
                        </tr>
                        <tr>
                            <td>6.50</td>
                            <td>K-10½</td>
                            <td>3</td>
                            <td>Bulky</td>
                            <td>Quick projects</td>
                            <td>Larger grip for comfort</td>
                        </tr>
                        <tr>
                            <td>8.00</td>
                            <td>L-11</td>
                            <td>0</td>
                            <td>Super Bulky</td>
                            <td>Chunky blankets</td>
                            <td>Ergonomic grip helpful</td>
                        </tr>
                        <tr>
                            <td>10.00</td>
                            <td>N/P</td>
                            <td>00</td>
                            <td>Super Bulky, Jumbo</td>
                            <td>Fast blankets, rugs</td>
                            <td>Often plastic or wood</td>
                        </tr>
                    </tbody>
                </table>
                </div>
                
                <div style="margin-top: 25px; background-color: rgba(218, 160, 109, 0.1); padding: 15px; border-radius: 10px;">
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">Hook Anatomy</h4>
                    <p style="margin-bottom: 10px;">Understanding the parts of your hook can help you use it more effectively:</p>
                    <ul style="list-style-type: disc; padding-left: 20px;">
                        <li><strong>Head/Point:</strong> The tapered end where you insert the hook into stitches</li>
                        <li><strong>Throat:</strong> The curved part that holds the yarn</li>
                        <li><strong>Shaft:</strong> The working area where stitches are formed</li>
                        <li><strong>Grip:</strong> The area you hold (may be ergonomically designed)</li>
                        <li><strong>Handle:</strong> The end portion, often thicker on ergonomic hooks</li>
                    </ul>
                    <p style="margin-top: 10px;"><em>Note: The material of your hook affects your crocheting experience. Metal hooks are slick and fast, while bamboo or wooden hooks grip yarn more and may be better for slippery yarns.</em></p>
                </div>
            </div>
            
            <!-- D. Stitch Library Quick Reference -->
            <div class="resource-section">
                <h3>Essential Stitch Library</h3>
                <p>This quick reference shows common crochet stitches, how they're written in patterns, and their typical uses. For detailed instructions on how to make these stitches, refer to the Crochet Mastery book or video tutorials.</p>
                
                <div class="table-responsive">
                    <table class="resource-table">
                    <thead>
                        <tr>
                            <th>Stitch Name</th>
                            <th>Abbreviation</th>
                            <th>Symbol (Description)</th>
                            <th>Description/Texture</th>
                            <th>Difficulty</th>
                            <th>Common Uses</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Chain</td>
                            <td>ch</td>
                            <td>Small circle</td>
                            <td>Foundation, open</td>
                            <td>Beginner</td>
                            <td>Starting rows</td>
                        </tr>
                        <tr>
                            <td>Slip Stitch</td>
                            <td>sl st</td>
                            <td>Dot with connecting line</td>
                            <td>Flat, joining</td>
                            <td>Beginner</td>
                            <td>Joining, edges</td>
                        </tr>
                        <tr>
                            <td>Single Crochet</td>
                            <td>sc</td>
                            <td>Plus sign</td>
                            <td>Dense, firm</td>
                            <td>Beginner</td>
                            <td>Amigurumi, dishcloths</td>
                        </tr>
                        <tr>
                            <td>Half Double Crochet</td>
                            <td>hdc</td>
                            <td>T-shape</td>
                            <td>Medium density</td>
                            <td>Beginner</td>
                            <td>Hats, quick projects</td>
                        </tr>
                        <tr>
                            <td>Double Crochet</td>
                            <td>dc</td>
                            <td>T-shape with extra line</td>
                            <td>Open, drapes well</td>
                            <td>Beginner</td>
                            <td>Blankets, garments</td>
                        </tr>
                        <tr>
                            <td>Treble Crochet</td>
                            <td>tr</td>
                            <td>T-shape with two extra lines</td>
                            <td>Very open, tall</td>
                            <td>Intermediate</td>
                            <td>Lacy items</td>
                        </tr>
                        <tr>
                            <td>Shell</td>
                            <td>shell</td>
                            <td>Multiple stitches from one point</td>
                            <td>Decorative, scalloped</td>
                            <td>Intermediate</td>
                            <td>Decorative edges</td>
                        </tr>
                        <tr>
                            <td>Cluster</td>
                            <td>cl</td>
                            <td>Multiple stitches joined at top</td>
                            <td>Textured, raised</td>
                            <td>Intermediate</td>
                            <td>Texture, dimension</td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
            
            <!-- Mobile guidance message -->
            <div class="resource-section" style="margin-top: 40px; padding: 15px; background-color: rgba(255, 127, 80, 0.1); border-radius: 10px; text-align: center;">
                <h3 style="color: var(--yarn-color);">Mobile Users</h3>
                <p>👉 Swipe left and right on tables to see all columns. 👈</p>
                <p>Tables are scrollable horizontally on smaller screens to ensure you can view all information.</p>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq-page-section" class="content-section">
            <h2 class="section-title">Leola's Crochet Clinic: Frequently Asked Questions</h2>
            
            <div class="faq-controls">
                <button class="read-all-btn" id="read-all-faqs">Have Agent Lee Read All FAQs</button>
                <button class="read-all-btn" id="stop-faq-reading">Stop Reading</button>
            </div>
            
            <div class="faq-grid">
                <div class="faq-item" data-faq-id="1">
                    <div class="faq-question">How do I know what size crochet hook to use?</div>
                    <div class="faq-answer">The best hook size depends on your yarn and project. Check your yarn label for a recommended hook size. For most beginners, medium weight (worsted) yarn with a 5.0mm (H-8) hook is a great starting point. Remember, your personal tension affects your work too - if your stitches are too tight, go up a hook size; if too loose, go down a size.</div>
                </div>
                
                <div class="faq-item" data-faq-id="2">
                    <div class="faq-question">Why does my project keep getting wider?</div>
                    <div class="faq-answer">This usually happens when you're adding extra stitches unintentionally. Common causes include: 1) Missing the first or last stitch of a row, 2) Not counting stitches regularly, 3) Accidentally working into chain spaces instead of stitches. Try counting your stitches after each row, using stitch markers at the beginning and end of rows, or following stitch counts provided in your pattern.</div>
                </div>
                
                <div class="faq-item" data-faq-id="3">
                    <div class="faq-question">My edges look uneven. What am I doing wrong?</div>
                    <div class="faq-answer">Uneven edges usually come from inconsistent tension or missing the first/last stitch of rows. Try these fixes: 1) Use stitch markers to mark the first and last stitch of each row, 2) Be consistent with your turning chains, 3) Count stitches regularly, 4) Practice maintaining even tension by being mindful of how tightly you hold the yarn. With practice, your edges will become straighter!</div>
                </div>
                
                <div class="faq-item" data-faq-id="4">
                    <div class="faq-question">How do I read a crochet pattern?</div>
                    <div class="faq-answer">Crochet patterns use standardized abbreviations and formatting. Start by reviewing the abbreviation key included with the pattern. Numbers before stitches (like "3 dc") mean to make that number of stitches. Asterisks (*) indicate repeated sections - repeat everything between the asterisks and the next indicator (often "to" or "repeat from *"). Brackets [ ] or parentheses ( ) group instructions to be worked into the same stitch or space. Always read through the entire pattern before starting.</div>
                </div>
                
                <div class="faq-item" data-faq-id="5">
                    <div class="faq-question">What's the difference between US and UK crochet terms?</div>
                    <div class="faq-answer">US and UK terms use different names for the same stitches, which can cause confusion. For example, a US single crochet (sc) is called a double crochet (dc) in UK terms. A US double crochet (dc) is a treble crochet (tr) in UK. Always check which terms your pattern uses - look for clues like "US terms used" or the publisher's location. If in doubt, look for stitch descriptions or illustrations that can clarify which stitches are intended.</div>
                </div>
                
                <div class="faq-item" data-faq-id="6">
                    <div class="faq-question">Why is gauge important and how do I check it?</div>
                    <div class="faq-answer">Gauge ensures your project will be the correct size. To check gauge, crochet a swatch following the gauge instructions (e.g., "16 sc × 18 rows = 4×4 inches"). After completing the swatch, measure how many stitches and rows you have in 4 inches. If you have more stitches/rows than stated, your stitches are too small - use a larger hook. If you have fewer, your stitches are too large - use a smaller hook. Adjust until your gauge matches the pattern.</div>
                </div>
                
                <div class="faq-item" data-faq-id="7">
                    <div class="faq-question">How do I join a new yarn or change colors?</div>
                    <div class="faq-answer">To join new yarn: Work until last yarn over of the stitch before the color change, leave the last two loops of old color on hook, yarn over with new color and pull through both loops. Continue with new color. Weave in ends later. Alternative method: Stop with the old color, leaving a 6-inch tail. Make a slip knot with new color, place on hook, and continue pattern. For invisible color changes in amigurumi, complete the stitch before the change with old color, then use new color for next stitch.</div>
                </div>
                
                <div class="faq-item" data-faq-id="8">
                    <div class="faq-question">What's the best way to weave in ends?</div>
                    <div class="faq-answer">For secure ends that won't unravel: 1) Leave 6-inch tails when changing colors or finishing, 2) Use a tapestry needle to weave ends through the back of stitches, following the path of the yarn, 3) Change direction at least once while weaving, 4) Weave through at least 3-4 inches of stitches, 5) Gently stretch work to ensure ends are secure, 6) Trim any excess. For extra security, weave through the same path twice before trimming.</div>
                </div>
                
                <div class="faq-item" data-faq-id="9">
                    <div class="faq-question">Why is my project curling or not lying flat?</div>
                    <div class="faq-answer">Curling usually indicates tension issues or incorrect stitch counts. For flat items: 1) Check that you're working the correct number of stitches in each row, 2) Ensure you're using the appropriate turning chain height for your stitches, 3) Try using a larger hook if tension is too tight, 4) Block your finished piece by wetting or steaming it and pinning it flat to dry. Remember that some stitch patterns naturally have more texture or curl than others.</div>
                </div>
                
                <div class="faq-item" data-faq-id="10">
                    <div class="faq-question">What does "work in rounds" vs "work in rows" mean?</div>
                    <div class="faq-answer">Working in rows means you crochet back and forth, turning your work at the end of each row. This creates flat pieces with distinct right/wrong sides. Working in rounds means you crochet continuously in circles without turning, typically joining each round with a slip stitch or working in a spiral. This creates tubes or circular shapes. Amigurumi and hats are often worked in rounds, while scarves and blankets typically work in rows.</div>
                </div>
                
                <div class="faq-item" data-faq-id="11">
                    <div class="faq-question">How do I block my finished crochet project?</div>
                    <div class="faq-answer">Blocking helps shape and set your project. For most fibers: 1) Wash item according to yarn instructions, 2) Gently squeeze out excess water (don't wring), 3) Lay on a flat surface on towels, 4) Shape to correct dimensions, pinning if needed, 5) Allow to dry completely. For delicate items or quick results, you can steam block by holding a steamer above the pinned piece without touching it. Acrylic yarns need "kill" blocking with more direct heat to permanently reshape.</div>
                </div>
                
                <div class="faq-item" data-faq-id="12">
                    <div class="faq-question">What's the best yarn for beginners?</div>
                    <div class="faq-answer">Beginners should start with medium weight (worsted/category 4) yarn in a light color. Light colors make it easier to see your stitches. Choose smooth, non-splitty yarn with minimal texture or fuzziness. Wool/acrylic blends or 100% acrylic yarns like Red Heart Super Saver or Caron Simply Soft are great choices - they're affordable, available in many colors, and forgiving to work with. Avoid dark colors, fuzzy yarns, or novelty yarns until you're more comfortable with the basics.</div>
                </div>
                
                <div class="faq-item" data-faq-id="13">
                    <div class="faq-question">My hands hurt when I crochet. What can I do?</div>
                    <div class="faq-answer">Hand pain often comes from tension or poor ergonomics: 1) Take frequent breaks (5-10 minutes every 30 minutes), 2) Try ergonomic hooks with larger handles, 3) Loosen your grip - you shouldn't be squeezing tightly, 4) Adjust how you hold the yarn for less finger strain, 5) Do hand stretches before and during crochet sessions, 6) Check your posture - sit with back supported and project at a comfortable height. If pain persists, consult a healthcare provider as it could be a repetitive stress injury.</div>
                </div>
                
                <div class="faq-item" data-faq-id="14">
                    <div class="faq-question">What does "work in back loop only" or "front loop only" mean?</div>
                    <div class="faq-answer">Each crochet stitch has a V-shape at the top with two loops - a front loop (closest to you) and a back loop (farthest from you). Normally, you insert your hook under both loops when making a stitch. "Work in back loop only" (BLO) means to insert your hook only under the back loop. "Work in front loop only" (FLO) means to insert your hook only under the front loop. These techniques create texture, ridges, or stretch in your fabric and are often used for ribbing or decorative elements.</div>
                </div>
                
                <div class="faq-item" data-faq-id="15">
                    <div class="faq-question">How do I fix a mistake without starting over?</div>
                    <div class="faq-answer">For small mistakes: 1) "Frog" (rip it, rip it) by pulling out stitches to the row with the error, then re-crochet, 2) For a missed stitch, add an increase in that spot on the next row, 3) For an extra stitch, work a decrease in that spot on the next row. Always place a stitch marker in the last correct stitch before frogging. For major mistakes in complex patterns, sometimes starting over saves time and frustration. As you gain experience, you'll develop an eye for which mistakes are noticeable and which can be cleverly fixed.</div>
                </div>
                
                <div class="faq-item" data-faq-id="16">
                    <div class="faq-question">What's the difference between crochet and knitting?</div>
                    <div class="faq-answer">Crochet uses one hook and works with one active loop at a time, while knitting uses two needles with many active loops. Crochet creates a thicker, more textured fabric that uses about 1/3 more yarn than knitting. Crochet is often easier for beginners to learn because you only manage one loop at a time, making mistakes easier to fix. Crochet excels at three-dimensional projects like amigurumi and complex textures, while knitting creates stretchier, drapier fabrics ideal for garments. Many crafters enjoy both techniques for their different strengths!</div>
                </div>
            </div>
        </div>        <!-- Tutorials Section -->
        <div id="tutorials-section" class="content-section">
            <h2 class="section-title">Video Tutorials</h2>
            
            <!-- Beginner Tutorials Category -->
            <div class="tutorial-category">
                <h3 style="color: var(--primary-color); margin-bottom: 20px;">🧶 Crochet Tutorials for Beginners</h3>
                
                <div class="video-embed-card">
                    <h4>How to Crochet for Absolute Beginners: Part 1</h4>
                    <p>A comprehensive guide covering the basics of crochet.</p>
                    <a href="https://www.youtube.com/watch?v=aAxGTnVNJiE" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>How To Crochet | VERY SLOW DEMONSTRATION</h4>
                    <p>A slow-paced tutorial ideal for beginners.</p>
                    <a href="https://www.youtube.com/watch?v=jUIfV-Qcq2c" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Easy Crochet Clutch - Faux Macrame!</h4>
                    <p>Learn to create a stylish clutch with a macrame-inspired design.</p>
                    <a href="https://www.youtube.com/watch?v=KatJfGNXcd4" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>
                
                <!-- Donation Reminder -->
                <div class="donation-reminder">
                    <p>Help us preserve and share Sister Lee's wisdom and techniques with future generations. 
                    <a href="donations.html" class="donate-link">Support Leola's Library with a donation</a></p>
                </div>
            </div>

            <!-- Support Section -->
            <div class="support-banner">
                <h3>Support Leola's Library</h3>
                <p>Your generous donations make it possible for Sister Lee to continue sharing her knowledge and passion for crochet with our community.</p>
                <a href="donations.html" class="donate-button">Make a Donation</a>
            </div>

            <!-- Macrame-Inspired Projects Category -->
            <div class="tutorial-category">
                <h3 style="color: var(--primary-color); margin-bottom: 20px;">🪢 Macrame-Inspired Crochet Projects</h3>
                
                <div class="video-embed-card">
                    <h4>DIY Crocheted Trivets using Macramé Cord</h4>
                    <p>Create durable trivets using macrame cord.</p>
                    <a href="https://www.youtube.com/watch?v=Li5OnLkxMC4" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Crochet Basket with Macrame Cord</h4>
                    <p>Craft a functional basket blending crochet and macrame techniques.</p>
                    <a href="https://www.youtube.com/watch?v=MuGymcu8uBU" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Macrame Inspired Wall Hanging with Tassels</h4>
                    <p>Design a decorative wall hanging incorporating macrame elements.</p>
                    <a href="https://www.youtube.com/watch?v=HlHWEjFP1ZE" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Macrame Inspired Crochet Plant Hanger</h4>
                    <p>Learn to make a plant hanger combining crochet and macrame styles.</p>
                    <a href="https://www.youtube.com/watch?v=qcBBOgfcalU" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>
            </div>

            <!-- More Inspiration Category -->
            <div class="tutorial-category">
                <h3 style="color: var(--primary-color); margin-bottom: 20px;">✨ More Crochet Inspiration</h3>
                
                <div class="video-embed-card">
                    <h4>Crochet Flower Tutorial</h4>
                    <p>Crafting Elegance: Crochet Flower Mastery with Lace Accents!<br>
                    <span style="color:#9ca3af;font-size:0.9rem;">Crochet • Feb 6, 2024</span></p>
                    <a href="https://www.youtube.com/watch?v=kCwhmWiMh44" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Mini Granny Square Bag Tutorial</h4>
                    <p>Micro Mastery: Crochet Your Own Mini Granny Square Bag<br>
                    <span style="color:#9ca3af;font-size:0.9rem;">MissXade • Sep 26, 2023</span></p>
                    <a href="https://www.youtube.com/watch?v=sF33vU8sytQ" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Easy Crochet Method</h4>
                    <p>Haven't You Mastered This Easy Crochet Method Yet?<br>
                    <span style="color:#9ca3af;font-size:0.9rem;">The Magic Yarn • Nov 30, 2024</span></p>
                    <a href="https://www.youtube.com/watch?v=-KcPr4w9fGM" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>

                <div class="video-embed-card">
                    <h4>Granny Square Tutorial</h4>
                    <p>Discover the Art of Crochet: Beginner's Guide to Granny Square Mastery<br>
                    <span style="color:#9ca3af;font-size:0.9rem;">crochet_college • Jul 8, 2024</span></p>
                    <a href="https://www.youtube.com/watch?v=DRuByM4_jHI" target="_blank" class="channel-button">
                        Watch on YouTube
                    </a>
                </div>
                
                <!-- Donation Reminder -->
                <div class="donation-reminder">
                    <p>Your support helps Leola's Library continue creating free educational content for everyone. 
                    <a href="donations.html" class="donate-link">Please consider donating today!</a></p>
                </div>
            </div>
        </div>
        

        
        <!-- Contact Section -->
        <div id="contact-section" class="content-section">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>Have questions about crochet? Reach out to Sister Lee:</p>
                    
                    <div class="contact-detail">
                        <div class="contact-icon">📱</div>
                        <div>
                            <strong>Phone</strong><br>
                            <span class="contact-text">(*************</span>
                        </div>
                    </div>
                    
                    <div class="contact-detail">
                        <div class="contact-icon">📍</div>
                        <div>
                            <strong>Address</strong><br>
                            23rd and Locust<br>
                            Milwaukee, WI 53206
                        </div>
                    </div>
                    

                </div>
                
                <div class="inspiration-card">
                    <h3>Leola's Inspiration</h3>
                    <blockquote>
                        "Stay inspired, always. Believe in yourself and your unique gifts. Never give up on new adventures, even when the path seems unclear. When things get hard or complicated, remember that each stitch — like each moment in life — builds something beautiful with time and patience. Always build yourself up and believe in your work and your craft. Your hands have the power to create what your heart envisions."
                    </blockquote>
                    <div class="quote-signature">— Sister Leola Lee</div>
                </div>
            </div>
        </div>
    <!-- Donations Section -->
    <div id="donate-section" class="content-section">
        <h2 class="section-title">Support Leola's Crochet World</h2>
        
        <p style="text-align: center; max-width: 800px; margin: 0 auto 40px; font-size: 1.1rem;">
            Your generous donations help Sister Lee continue creating educational crochet content, maintaining this website, and sharing the art of crochet with our community. Every contribution, no matter the size, makes a difference in our mission to preserve and pass on this beautiful craft.
        </p>
        
        <div class="support-banner">
            <h3>Please Visit Our Donation Page</h3>
            <p>We've moved our donation system to a dedicated page where you can contribute securely.</p>
            <a href="donations.html" class="donate-button">Go to Donation Page</a>
        </div>
        
        <div class="donation-cards">
            <div class="donation-card">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5-1.95 0-4.05.4-5.5 1.5v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z'/%3E%3C/svg%3E" alt="Educational Materials Icon">
                <h3>Educational Materials</h3>
                <p>Your donation helps create new books, tutorials, and instructional materials for crocheters of all levels.</p>
            </div>
            
            <div class="donation-card">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.17-5.24l-1.1-1.1c.71-1.33.53-3.01-.59-4.13C13.79 8.84 12.9 8.5 12 8.5c-.03 0-.06.01-.09.01L13 9.6l-1.06 1.06-2.83-2.83L11.94 5 13 6.06l-.96.94c1.27-.01 2.54.45 3.54 1.46 1.71 1.71 1.92 4.35.59 6.3z'/%3E%3C/svg%3E" alt="Community Workshops Icon">
                <h3>Community Workshops</h3>
                <p>Support free community workshops and crochet classes for those who can't afford traditional instruction.</p>
            </div>
            
            <div class="donation-card">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z'/%3E%3C/svg%3E" alt="Website Maintenance Icon">
                <h3>Website Maintenance</h3>
                <p>Help keep our digital resources accessible, up-to-date, and free for everyone to use.</p>
            </div>
        </div>
        
        <div class="donation-form-container">
            <h2>Make a Donation</h2>
            <form id="donation-form" action="#" method="post">
                <div class="form-group">
                    <label for="name">Your Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label>Select Donation Amount</label>
                    <div class="amount-options">
                        <div class="amount-option">
                            <input type="radio" id="amount-10" name="amount" value="10" checked>
                            <label for="amount-10">$10</label>
                        </div>
                        
                        <div class="amount-option">
                            <input type="radio" id="amount-25" name="amount" value="25">
                            <label for="amount-25">$25</label>
                        </div>
                        
                        <div class="amount-option">
                            <input type="radio" id="amount-50" name="amount" value="50">
                            <label for="amount-50">$50</label>
                        </div>
                        
                        <div class="amount-option">
                            <input type="radio" id="amount-100" name="amount" value="100">
                            <label for="amount-100">$100</label>
                        </div>
                        
                        <div class="amount-option">
                            <input type="radio" id="amount-custom" name="amount" value="custom">
                            <label for="amount-custom">Other</label>
                        </div>
                    </div>
                    
                    <div class="form-group" id="custom-amount-container" style="display: none;">
                        <label for="custom-amount">Custom Amount ($)</label>
                        <input type="number" id="custom-amount" name="custom-amount" placeholder="Enter amount" min="1">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="message">Message (Optional)</label>
                    <textarea id="message" name="message" rows="4"></textarea>
                </div>
                
                <button type="submit">Donate Now</button>
            </form>
            
            <div class="stripe-container">
                <p>Or donate quickly with Stripe:</p>
                <a href="https://buy.stripe.com/7sI0382DR9075u87sw" target="_blank" rel="noopener noreferrer" class="stripe-button">
                    🎁 Donate to Support Our Work
                </a>
            </div>
        </div>
        
        <div class="testimonials">
            <h2>What Our Supporters Say</h2>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <div class="testimonial-text">
                        Sister Lee's crochet lessons changed my life. Supporting her work is my way of saying thank you for all she's given to our community.
                    </div>
                    <div class="testimonial-author">
                        - Maria Johnson, Supporter since 2020
                    </div>
                </div>
                
                <div class="testimonial">
                    <div class="testimonial-text">
                        When I donate to Leola's Crochet World, I know I'm helping preserve traditional craft skills and passing them on to the next generation.
                    </div>
                    <div class="testimonial-author">
                        - Thomas Wright, Monthly Donor
                    </div>
                </div>
                
                <div class="testimonial">
                    <div class="testimonial-text">
                        The free resources here helped me learn crochet during a difficult time. Now that I can, I give back so others can have the same opportunity.
                    </div>
                    <div class="testimonial-author">
                        - Aisha Patel, Community Member
                    </div>
                </div>
            </div>
        </div>
    </div>
    </main>
    
    <!-- Flipbook Modal -->
    <div class="flipbook-modal" id="flipbook-modal">
        <div class="flipbook-container" id="flipbook-container">
            <button class="close-flipbook" id="close-flipbook">&times;</button>
            <div id="book-cover-container" style="width:100%; height:100%; display:flex; justify-content:center; align-items:center; overflow:hidden;">
                <img id="book-cover-image" style="max-width:100%; max-height:100%; object-fit:contain; cursor:pointer;" alt="Book Cover">
            </div>
            <iframe id="flipbook-iframe" style="width:100%; height:100%; border:none; display:none;"></iframe>
        </div>
    </div>
    
    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <img src="bmmq6xrkp3.png" alt="Leola's Library Logo" style="width: 120px; height: auto; margin-right: 20px; filter: brightness(1.3);">
                    <div>
                        <h3 style="margin-bottom: 5px;">Leola's Library</h3>
                        <p style="margin: 0; font-style: italic;">Where every stitch tells a story</p>
                    </div>
                </div>
                <p>Your go-to destination for crochet tutorials, patterns, and interactive books. Visit our library and let Agent Lee assist you.</p>
            </div>
            
            <div class="footer-column">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="#home" data-section="home-section">Home</a></li>
                    <li><a href="#books" data-section="books-section">Books</a></li>
                    <li><a href="#games" data-section="games-section">Games</a></li>
                    <li><a href="#about" data-section="about-section">About</a></li>
                    <li><a href="#resources" data-section="resources-section">Resources</a></li>
                    <li><a href="#tutorials" data-section="tutorials-section">Tutorials</a></li>
                    <li><a href="#faq" data-section="faq-page-section">FAQ</a></li>
                    <li><a href="#contact" data-section="contact-section">Contact</a></li>
                    <li><a href="donations.html">Donate</a></li>
                </ul>
            </div>
            
            <div class="footer-column">
                <h3>Newsletter</h3>
                <p>Subscribe to our newsletter for the latest patterns, tutorials, and crochet inspiration.</p>
                <form action="#" method="POST">
                    <div style="margin-bottom: 15px;">
                        <input type="email" placeholder="Your email address" style="width: 100%; padding: 10px; border-radius: 20px; border: 1px solid var(--secondary-color);" required>
                    </div>
                    <button type="submit" style="background-color: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">Subscribe</button>
                </form>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2025 Leola's Crochet World. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/agent-lee-final.js"></script>
    <script src="js/badge-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - initializing navigation');
            
            // DO NOT STORE the original speakText function - use the one from Agent Lee directly
            // This ensures we always use the Emma voice enforcement
            
            // Check for visit status
            const hasVisited = localStorage.getItem('hasVisitedLeolaLibrary');
            const readNeedleYarn = localStorage.getItem('read_needle_and_yarn');
            const readCrochetMastery = localStorage.getItem('read_crochet_mastery');
            
            // Set up flag if this is the first visit
            if (!hasVisited) {
                localStorage.setItem('hasVisitedLeolaLibrary', 'true');
            }
            
            // Navigation functionality - simplified and unified
            const navLinks = document.querySelectorAll('.nav-link');
            const overviewLinks = document.querySelectorAll('.overview-link');
            const ctaButtons = document.querySelectorAll('.cta-button[data-section]');
            const footerLinks = document.querySelectorAll('.footer-column a[data-section]');
            
            console.log('Found navigation elements:', {
                navLinks: navLinks.length,
                overviewLinks: overviewLinks.length,
                ctaButtons: ctaButtons.length,
                footerLinks: footerLinks.length
            });
            
            // Main navigation links
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    console.log('Nav link clicked:', sectionId);
                    if (sectionId) {
                        showSection(sectionId);
                        history.pushState(null, "", this.getAttribute('href'));
                    } else {
                        console.warn('No data-section found for nav link:', this);
                    }
                });
            });
            
            // Overview card links
            overviewLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    console.log('Overview link clicked:', sectionId);
                    if (sectionId) {
                        showSection(sectionId);
                    } else {
                        console.warn('No data-section found for overview link:', this);
                    }
                });
            });
            
            // CTA buttons
            ctaButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    console.log('CTA button clicked:', sectionId);
                    if (sectionId) {
                        showSection(sectionId);
                    } else {
                        console.warn('No data-section found for CTA button:', this);
                    }
                });
            });
            
            // Footer links
            footerLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    if (sectionId) {
                        showSection(sectionId);
                        history.pushState(null, "", this.getAttribute('href'));
                    }
                });
            });
            
            // Show section function
            function showSection(sectionIdToShow) {
                console.log('Showing section:', sectionIdToShow);
                
                // Hide all sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                    section.style.display = 'none'; // Ensure sections are hidden
                });
                
                // Show target section
                const targetSection = document.getElementById(sectionIdToShow);
                if (targetSection) {
                    targetSection.classList.add('active');
                    targetSection.style.display = 'block'; // Ensure section is visible
                    console.log('Section shown successfully:', sectionIdToShow);
                } else {
                    console.error('Section not found:', sectionIdToShow);
                }
                
                // Update active nav link
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-section') === sectionIdToShow) {
                        link.classList.add('active');
                    }
                    
                    // Special case for donate link (don't remove active if it's a direct page link)
                    if (link.getAttribute('href') && link.getAttribute('href').endsWith('.html')) {
                        // Don't modify these links as they go to separate pages
                    }
                });
                
                // Stop any ongoing narration
                if (window.speechSynthesis) {
                    window.speechSynthesis.cancel();
                }
                
                // Trigger Agent Lee's contextual message for certain sections
                if (sectionIdToShow === 'home-section') {
                    if (originalSpeakText) {
                        originalSpeakText(getHomePageGreeting());
                    }
                } else if (sectionIdToShow === 'faq-page-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Welcome to our Frequently Asked Questions! Click on any question to hear the answer, or ask me to read them all.");
                    }
                } else if (sectionIdToShow === 'resources-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Welcome to the resources section! Here you'll find helpful information about crochet patterns, yarn types, hook sizes, and common stitches. Let me know if you need help understanding any of these guides.");
                    }
                } else if (sectionIdToShow === 'books-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Welcome to our book collection! Here you'll find Sister Lee's wonderful interactive books. 'Needle & Yarn' is a heartwarming story of friendship and creativity, while 'Crochet Mastery' is your complete guide from beginner to expert. Which would you like to explore?");
                    }
                } else if (sectionIdToShow === 'about-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Let me tell you about Sister Leola Lee - a remarkable woman whose life is stitched with purpose and creativity. Born in Mississippi and living in Milwaukee since 1985, she's a mother, grandmother, great-grandmother, and master crocheter who has dedicated her life to teaching and inspiring others through the art of crochet.");
                    }
                } else if (sectionIdToShow === 'games-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Welcome to our interactive games section! Here you can play fun crochet-themed games like the Interactive Crochet Simulator and Stitch Match. These games will help you learn about crochet while having fun. Plus, you can earn badges to show your progress!");
                    }
                } else if (sectionIdToShow === 'tutorials-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Welcome to our tutorial collection! Would you like to support Leola's Library with a donation? Your contribution helps us maintain this site and create more educational content. Please visit our donation page to learn more.");
                    }
                } else if (sectionIdToShow === 'contact-section') {
                    if (originalSpeakText) {
                        originalSpeakText("Here's how you can get in touch with Sister Lee. You'll find her contact information and an inspiring message about believing in yourself and your creative gifts. Feel free to reach out with any questions about crochet or her books.");
                    }
                }
                
                // Scroll to top of page
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
            
            // Generate contextual home greeting based on visit history
            function getHomePageGreeting() {
                const hasVisited = localStorage.getItem('hasVisitedLeolaLibrary');
                if (!hasVisited || hasVisited === 'new') {
                    localStorage.setItem('hasVisitedLeolaLibrary', 'true');
                    return `Welcome to Leola's Digital Library! I'm Agent Lee, your guide to our content.
                    
                    Here, every page holds a purpose — and every stitch, a story. You'll find two wonderful books: 'Needle & Yarn: A Love Stitched in Time', a heartwarming tale, and 'Crochet Mastery: A Complete Guide', which teaches crochet from beginner to expert.
                    
                    We also have comprehensive crochet resources, interactive games, and video tutorials to help you master the craft. If you have questions, our FAQ section has answers to common crochet questions.
                    
                    Feel free to navigate using the menu above, and I'll be here if you need assistance.`;
                } else {
                    const readNeedleYarn = localStorage.getItem('read_needle_and_yarn');
                    const readCrochetMastery = localStorage.getItem('read_crochet_mastery');
                    let message = "Welcome back to Leola's Library, crafty friend! ";
                    
                    if (readNeedleYarn && !readCrochetMastery) {
                        message += "You've explored the heartwarming tale of Needle & Yarn. Ready to dive into Crochet Mastery, or perhaps revisit some resources?";
                    } else if (!readNeedleYarn && readCrochetMastery) {
                        message += "You've delved into the secrets of Crochet Mastery! How about enjoying the charming story of Needle & Yarn next?";
                    } else if (readNeedleYarn && readCrochetMastery) {
                        message += "You're a true crochet enthusiast, having explored both books! What new project or resource can I help you find today?";
                    } else {
                        message += "Which crafting adventure calls to you today? The story of Needle & Yarn, the guide to Crochet Mastery, or perhaps some helpful resources?";
                    }
                    return message;
                }
            }
            
            // Handle initial load based on URL hash or default to home
            function handleInitialLoad() {
                const hash = window.location.hash;
                if (hash && hash.length > 1) {
                    const sectionId = hash.replace('#', '') + '-section';
                    const sectionExists = document.getElementById(sectionId);
                    
                    if (sectionExists) {
                        showSection(sectionId);
                    } else {
                        showSection('home-section');
                    }
                } else {
                    showSection('home-section');
                }
            }
            
            // Add CSS for video embedding
            const videoStyles = document.createElement('style');
            videoStyles.textContent = `
                .video-embed-card {
                    max-width: 100%;
                    margin-bottom: 2rem;
                    background: #fff;
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                    padding: 0 0 15px 0;
                }
                
                .video-thumbnail {
                    position: relative;
                    cursor: pointer;
                    width: 100%;
                }
                
                .video-thumbnail img {
                    width: 100%;
                    display: block;
                }
                
                .video-player-container {
                    display: none;
                    width: 100%;
                    aspect-ratio: 16/9;
                }
                
                .play-button-overlay {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 3rem;
                    color: white;
                    background: rgba(0,0,0,0.5);
                    border-radius: 50%;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .video-embed-card h4,
                .video-embed-card p {
                    padding: 0 15px;
                }
                
                .video-meta {
                    color: #777;
                    font-size: 0.9rem;
                    margin-top: 5px;
                    margin-bottom: 5px;
                }
            `;
            document.head.appendChild(videoStyles);
            
            // Convert existing video links to embedded format
            document.querySelectorAll('.video-embed-card a.channel-button').forEach(link => {
                // Get the parent card
                const card = link.closest('.video-embed-card');
                if (!card) return;
                
                // Extract video ID from the link
                const url = link.getAttribute('href');
                const videoId = url.match(/(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([^?&]+)/)?.[1];
                
                if (videoId) {
                    // Set the video ID on the card
                    card.setAttribute('data-video-id', videoId);
                    
                    // Get the title and description
                    const title = card.querySelector('h4')?.textContent || 'Crochet Tutorial';
                    const desc = card.querySelector('p')?.textContent || '';
                    
                    // Create and insert thumbnail container before the link
                    const thumbnailContainer = document.createElement('div');
                    thumbnailContainer.className = 'video-thumbnail';
                    thumbnailContainer.innerHTML = `
                        <img src="https://img.youtube.com/vi/${videoId}/hqdefault.jpg" alt="${title}">
                        <div class="play-button-overlay">▶</div>
                    `;
                    
                    // Create player container
                    const playerContainer = document.createElement('div');
                    playerContainer.className = 'video-player-container';
                    
                    // Replace the link with our new elements
                    link.parentNode.insertBefore(thumbnailContainer, link);
                    link.parentNode.insertBefore(playerContainer, link);
                    link.remove();
                }
            });
            
            // Video embed functionality
            document.querySelectorAll('.video-thumbnail').forEach(thumbnailContainer => {
                thumbnailContainer.addEventListener('click', function() {
                    // Get the parent card and video ID
                    const card = this.closest('.video-embed-card');
                    if (!card) return;
                    
                    const videoId = card.getAttribute('data-video-id');
                    const playerContainer = card.querySelector('.video-player-container');
                    
                    if (videoId && playerContainer) {
                        // Create and append iframe
                        const iframe = document.createElement('iframe');
                        iframe.setAttribute('src', `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`);
                        iframe.setAttribute('title', 'YouTube video player');
                        iframe.setAttribute('frameborder', '0');
                        iframe.setAttribute('width', '100%');
                        iframe.setAttribute('height', '100%');
                        iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share');
                        iframe.setAttribute('allowfullscreen', '');
                        
                        playerContainer.innerHTML = '';
                        playerContainer.appendChild(iframe);
                        this.style.display = 'none';
                        playerContainer.style.display = 'block';
                        
                        // Scroll the video into view
                        playerContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
            });
            
            // Call this on page load
            handleInitialLoad();
            
            // Initialize badge display in games section
            if (window.BadgeManager) {
                const gameBadgesContainer = document.getElementById('game-badges-container');
                if (gameBadgesContainer) {
                    BadgeManager.displayBadges('game-badges-container');
                    
                    // Hide "no badges" message if badges exist
                    const noBadgesMessage = document.getElementById('no-game-badges');
                    const earnedBadges = BadgeManager.getEarnedBadges();
                    if (earnedBadges.length > 0 && noBadgesMessage) {
                        noBadgesMessage.style.display = 'none';
                    }
                }
                
                // Listen for badge events from game iframes or windows
                document.addEventListener('badgeEarned', function(event) {
                    console.log('Badge earned event received in main page:', event.detail);
                    if (event.detail && event.detail.id) {
                        // Re-display badges when a new one is earned
                        setTimeout(function() {
                            if (gameBadgesContainer) {
                                BadgeManager.displayBadges('game-badges-container');
                                
                                // Hide "no badges" message
                                if (noBadgesMessage) {
                                    noBadgesMessage.style.display = 'none';
                                }
                            }
                        }, 1000); // Short delay to ensure badge is registered
                    }
                });
            }
            
            // Handle back/forward browser buttons
            window.addEventListener('popstate', handleInitialLoad);
            
            // Book launch functionality
            const bookLinks = document.querySelectorAll('.launch-book');
            
            bookLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault(); // Prevent default navigation
                    const book = this.getAttribute('data-book');
                    
                    // Set local storage to track which book was read
                    if (book === 'needle-yarn') {
                        localStorage.setItem('read_needle_and_yarn', 'true');
                        window.location.href = 'd6jq33mv39.html'; // Direct to needle and yarn book
                    } else if (book === 'crochet-mastery') {
                        localStorage.setItem('read_crochet_mastery', 'true');
                        window.location.href = '0lbzci75tc.html'; // Direct to crochet mastery book
                    }
                });
            });
            
            // FAQ functionality - simplified
            const faqItems = document.querySelectorAll('.faq-item');
            let currentlyReading = null;
            let currentFaqIndex = 0;
            
            faqItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Toggle the current item
                    this.classList.toggle('active');
                    
                    // If item becomes active, read it using Agent Lee's speakText with Emma voice
                    if (this.classList.contains('active') && window.speakText) {
                        const question = this.querySelector('.faq-question').textContent;
                        const answer = this.querySelector('.faq-answer').textContent;
                        window.speakText(question + ". " + answer);
                    }
                });
            });
            
            // Read All FAQs Button
            const readAllFaqsBtn = document.getElementById('read-all-faqs');
            const stopFaqReadingBtn = document.getElementById('stop-faq-reading');
            
            if (readAllFaqsBtn) {
                readAllFaqsBtn.addEventListener('click', function() {
                    // Start reading from the beginning
                    currentFaqIndex = 0;
                    readNextFaq();
                });
            }
            
            if (stopFaqReadingBtn) {
                stopFaqReadingBtn.addEventListener('click', function() {
                    // Stop reading
                    if (window.speechSynthesis) {
                        window.speechSynthesis.cancel();
                    }
                    currentlyReading = null;
                });
            }
            
            function readNextFaq() {
                // If we reached the end, stop
                if (currentFaqIndex >= faqItems.length) {
                    currentlyReading = null;
                    return;
                }
                
                // Get the current FAQ
                const currentFaq = faqItems[currentFaqIndex];
                currentFaq.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // Close all FAQs
                faqItems.forEach(item => item.classList.remove('active'));
                
                // Open the current one
                currentFaq.classList.add('active');
                
                // Read it
                const question = currentFaq.querySelector('.faq-question').textContent;
                const answer = currentFaq.querySelector('.faq-answer').textContent;
                const textToRead = question + ". " + answer;
                
                currentlyReading = textToRead;
                
                // Use speakText function from Agent Lee with strictly Emma voice
                if (window.speakText) {
                    // Use the current speakText function that enforces Emma voice
                    window.speakText(textToRead);
                    
                    // Set up a timer to check when speech ends
                    const checkSpeechInterval = setInterval(function() {
                        if (!window.speechSynthesis || !window.speechSynthesis.speaking) {
                            clearInterval(checkSpeechInterval);
                            // Wait a moment before moving to next FAQ
                            setTimeout(function() {
                                currentFaqIndex++;
                                readNextFaq();
                            }, 1500);
                        }
                    }, 300);
                } else {
                    // If speech isn't available, just advance
                    currentFaqIndex++;
                    setTimeout(readNextFaq, 3000);
                }
            }
            
            // Create image modal for thumbnail enlargement
            const imageModal = document.createElement('div');
            imageModal.className = 'image-modal';
            imageModal.innerHTML = `
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <img class="modal-image" src="" alt="">
                </div>
            `;
            document.body.appendChild(imageModal);

            // Author image gallery functionality
            const mainImage = document.querySelector('.main-author-image');
            const thumbnails = document.querySelectorAll('.thumbnail-image');
            
            if (mainImage && thumbnails.length > 0) {
                thumbnails.forEach(thumb => {
                    thumb.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        // Show the clicked image in full screen modal
                        const modalImage = imageModal.querySelector('.modal-image');
                        modalImage.src = thumb.src;
                        modalImage.alt = thumb.alt;
                        
                        // Show modal with animation
                        imageModal.classList.add('show');
                        
                        // Prevent body scrolling
                        document.body.style.overflow = 'hidden';
                    });
                });
            }

            // Close modal functionality
            const closeModal = imageModal.querySelector('.close-modal');
            
            function closeImageModal() {
                imageModal.classList.remove('show');
                document.body.style.overflow = 'auto';
            }

            closeModal.addEventListener('click', closeImageModal);
            
            // Close modal when clicking outside the image
            imageModal.addEventListener('click', (e) => {
                if (e.target === imageModal) {
                    closeImageModal();
                }
            });
            
            // Close modal with Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && imageModal.classList.contains('show')) {
                    closeImageModal();
                }
            });
            
            // Mobile menu functionality
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    const mainNav = document.querySelector('.main-navigation');
                    mainNav.classList.toggle('mobile-hidden');
                });
            }
        });
    </script>
    
    <script src="js/agentlee.js"></script>
    <script>
        // Fix donation link
        document.addEventListener('DOMContentLoaded', function() {
            const donateLink = document.getElementById('donate-link');
            if (donateLink) {
                donateLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'donations.html';
                });
            }
            
            // Also fix any other donation links
            document.querySelectorAll('a[href="donations.html"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'donations.html';
                });
            });
        });
        
        // Voice recognition for Agent Lee
        let recognition;
        
        // Check if speech recognition is supported
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            // Initialize speech recognition
            recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';
            
            // Handle results
            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                console.log('Voice command detected:', transcript);
                
                // Process voice commands
                processVoiceCommand(transcript);
            };
            
            // Handle errors
            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
            };
            
            // Restart listening after a short delay
            recognition.onend = function() {
                // Only restart if we're on a mobile device
                if (window.innerWidth <= 576) {
                    setTimeout(() => {
                        try {
                            recognition.start();
                        } catch (e) {
                            console.log('Recognition already started');
                        }
                    }, 1000);
                }
            };
            
            // Start listening on mobile devices
            document.addEventListener('DOMContentLoaded', function() {
                if (window.innerWidth <= 576) {
                    setTimeout(() => {
                        try {
                            recognition.start();
                            console.log('Voice recognition started on mobile');
                        } catch (e) {
                            console.error('Failed to start voice recognition:', e);
                        }
                    }, 2000);
                }
            });
            
            // Process voice commands
            function processVoiceCommand(command) {
                const lcCommand = command.toLowerCase();
                
                // Get the Agent Lee card and message input
                const agentLeeCard = document.getElementById('agent-lee-card');
                const messageInput = document.getElementById('message-input');
                
                // Check for common voice commands
                if (lcCommand.includes('agent lee') || lcCommand.includes('hey lee') || lcCommand.includes('hi lee')) {
                    // Expand the card if minimized
                    if (agentLeeCard && agentLeeCard.classList.contains('minimized')) {
                        const minToggle = document.getElementById('minimize-toggle');
                        if (minToggle) minToggle.click();
                    }
                    
                    // Book reading commands
                    if (lcCommand.includes('read this book') || lcCommand.includes('read the book')) {
                        if (window.location.href.includes('needle-yarn.html') || 
                            window.location.href.includes('crochet-mastery.html')) {
                            
                            // Click the speak button if available
                            const speakBtn = document.getElementById('speak-btn');
                            if (speakBtn) {
                                speakBtn.click();
                                
                                // Add a message to the chat
                                if (window.addMessage) {
                                    window.addMessage('Reading the current page for you. I can continue reading as you turn pages.', 'agent');
                                }
                                
                                // Minimize after a few seconds
                                setTimeout(() => {
                                    if (agentLeeCard && !agentLeeCard.classList.contains('minimized')) {
                                        const minToggle = document.getElementById('minimize-toggle');
                                        if (minToggle) minToggle.click();
                                    }
                                }, 5000);
                            }
                        }
                    } 
                    // Page navigation commands
                    else if (lcCommand.includes('next page')) {
                        const nextBtn = document.getElementById('next-btn');
                        if (nextBtn) nextBtn.click();
                    }
                    else if (lcCommand.includes('previous page') || lcCommand.includes('go back')) {
                        const prevBtn = document.getElementById('prev-btn');
                        if (prevBtn) prevBtn.click();
                    }
                    else if (lcCommand.includes('beginning')) {
                        // Reset to first page logic
                        if (window.pageFlip && typeof window.pageFlip.turnToPage === 'function') {
                            window.pageFlip.turnToPage(0);
                        }
                    }
                    // Information command
                    else if (lcCommand.includes('what is this about') || lcCommand.includes('tell me about this')) {
                        if (window.location.href.includes('needle-yarn.html')) {
                            if (window.speakText) {
                                window.speakText("Needle & Yarn: A Love Stitched in Time is a heartwarming tale of friendship and adventure, following the journey of Needle and Yarn as they create beautiful projects together.");
                            }
                        } else if (window.location.href.includes('crochet-mastery.html')) {
                            if (window.speakText) {
                                window.speakText("Crochet Mastery: A Complete Guide is a comprehensive tutorial that teaches crochet from beginner to expert levels, with step-by-step instructions.");
                            }
                        }
                    }
                    // Stop reading command
                    else if (lcCommand.includes('stop reading') || lcCommand.includes('stop narration')) {
                        if (window.speechSynthesis) {
                            window.speechSynthesis.cancel();
                        }
                        
                        // Also try to click stop button if available
                        const stopBtn = document.getElementById('stop-btn');
                        if (stopBtn) stopBtn.click();
                    }
                    else {
                        // Set the message in the input and trigger send
                        if (messageInput) {
                            messageInput.value = command;
                            
                            // Find and click the send button
                            const sendBtn = document.getElementById('send-button');
                            if (sendBtn) sendBtn.click();
                            
                            // Auto-minimize after a response (in mobile)
                            if (window.innerWidth <= 576) {
                                setTimeout(() => {
                                    if (agentLeeCard && !agentLeeCard.classList.contains('minimized')) {
                                        const minToggle = document.getElementById('minimize-toggle');
                                        if (minToggle) minToggle.click();
                                    }
                                }, 8000);
                            }
                        }
                    }
                }
            }
        } else {
            console.log('Speech Recognition API not supported in this browser');
        }
        
        // Initialize video players for tutorials section
        document.addEventListener('DOMContentLoaded', function() {
            // Handle donation form functionality
            // Show/hide custom amount input for donation form
            const amountRadios = document.querySelectorAll('input[name="amount"]');
            if (amountRadios.length > 0) {
                amountRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        const customAmountContainer = document.getElementById('custom-amount-container');
                        if (customAmountContainer) {
                            customAmountContainer.style.display = this.value === 'custom' ? 'block' : 'none';
                            
                            if (this.value === 'custom') {
                                document.getElementById('custom-amount').focus();
                            }
                        }
                    });
                });
            }
            
            // Handle donation form submission
            const donationForm = document.getElementById('donation-form');
            if (donationForm) {
                donationForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Get form values
                    const name = document.getElementById('name').value;
                    let amount = document.querySelector('input[name="amount"]:checked').value;
                    
                    // If custom amount is selected, use that value
                    if (amount === 'custom') {
                        amount = document.getElementById('custom-amount').value;
                        // Default to 10 if no custom amount entered
                        if (!amount || amount < 1) {
                            amount = 10;
                        }
                    }
                    
                    // Show a thank you message
                    alert(`Thank you, ${name}! Your donation of $${amount} helps us continue our mission.`);
                    
                    // Reset the form
                    this.reset();
                    if (document.getElementById('custom-amount-container')) {
                        document.getElementById('custom-amount-container').style.display = 'none';
                    }
                });
            }
            
            // Stripe donation link handler
            const stripeButton = document.querySelector('.stripe-container a');
            if (stripeButton) {
                stripeButton.addEventListener('click', function(e) {
                    // Get selected amount
                    let amount = document.querySelector('input[name="amount"]:checked').value;
                    
                    // If custom amount is selected, use that value
                    if (amount === 'custom') {
                        amount = document.getElementById('custom-amount').value;
                        // Default to 10 if no custom amount entered
                        if (!amount || amount < 1) {
                            amount = 10;
                        }
                    }
                    
                    // Open Stripe in a new window with the donation amount
                    window.open(`https://buy.stripe.com/7sI0382DR9075u87sw?amount=${amount}`, '_blank');
                });
            }
            
            // Handle video thumbnail clicks
            const videoThumbnails = document.querySelectorAll('.video-thumbnail');
            
            videoThumbnails.forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    const videoId = this.getAttribute('data-video-id');
                    const playerContainer = this.nextElementSibling;
                    
                    if (videoId && playerContainer) {
                        // Hide thumbnail
                        this.style.display = 'none';
                        
                        // Show player container
                        playerContainer.style.display = 'block';
                        
                        // Create iframe
                        playerContainer.innerHTML = `
                            <iframe 
                                width="100%" 
                                height="100%" 
                                src="https://www.youtube.com/embed/${videoId}?autoplay=1" 
                                title="YouTube video player" 
                                frameborder="0" 
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                allowfullscreen>
                            </iframe>
                        `;
                    }
                });
            });
            
            // Display badges in homepage container if available
            if (window.BadgeManager && typeof window.BadgeManager.displayBadges === 'function') {
                // Display badges in homepage container
                const badgeContainer = document.getElementById('badges-container');
                if (badgeContainer) {
                    window.BadgeManager.displayBadges('badges-container');
                }
                
                // Also display badges in homepage badges section
                const homepageBadgesContainer = document.getElementById('homepage-badges-container');
                if (homepageBadgesContainer) {
                    window.BadgeManager.displayBadges('homepage-badges-container');
                }
                
                // Award welcome badge to new visitors
                if (!localStorage.getItem('welcome_badge_awarded')) {
                    window.BadgeManager.awardBadge('award_welcome');
                    localStorage.setItem('welcome_badge_awarded', 'true');
                }
            }
        });
    </script>
</body>
</html>