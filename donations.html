<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Leola's Crochet World</title>
    <style>
        /* Simple Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* Base Styles */
        body {
            font-family: Georgia, serif;
            line-height: 1.6;
            color: #333;
            background-color: #FAF3E0;
            padding: 0;
            margin: 0;
            min-height: 100vh;
        }
        
        /* Header */
        header {
            background-color: #FFF8E8;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 10;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .logo {
            display: inline-block;
            margin-right: 20px;
            vertical-align: middle;
        }
        
        .logo img {
            height: 50px;
            width: auto;
        }
        
        /* Navigation */
        nav {
            display: inline-block;
            vertical-align: middle;
            margin-top: 10px;
        }
        
        nav a {
            color: #8B4513;
            text-decoration: none;
            padding: 10px 15px;
            margin: 0 5px;
            font-weight: bold;
            display: inline-block;
            transition: color 0.3s ease;
        }
        
        nav a:hover, nav a.active {
            color: #FF7F50;
        }
        
        /* Main Content */
        .main-content {
            padding: 40px 0;
        }
        
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        /* Donation Cards */
        .donation-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .donation-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 300px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .donation-card h3 {
            color: #8B4513;
            margin-bottom: 15px;
        }
        
        .donation-card img {
            width: 60px;
            height: 60px;
            margin-bottom: 15px;
        }
        
        /* Donation Form */
        .donation-form-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .donation-form-container h2 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #8B4513;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="number"],
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
            font-size: 1rem;
        }
        
        .amount-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .amount-option {
            flex: 1;
            min-width: 70px;
        }
        
        .amount-option input[type="radio"] {
            display: none;
        }
        
        .amount-option label {
            display: block;
            padding: 10px;
            text-align: center;
            background-color: #f8f8f8;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .amount-option input[type="radio"]:checked + label {
            background-color: #FF7F50;
            color: white;
        }
        
        button[type="submit"] {
            background-color: #8B4513;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 1.1rem;
            display: block;
            margin: 0 auto;
            transition: background-color 0.3s;
        }
        
        button[type="submit"]:hover {
            background-color: #FF7F50;
        }
        
        /* Stripe Button */
        .stripe-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .stripe-button {
            background-color: #635bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            font-weight: bold;
            text-decoration: none;
            font-size: 16px;
            font-family: sans-serif;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .stripe-button:hover {
            background-color: #524bb4;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        
        /* Testimonials */
        .testimonials {
            margin-top: 40px;
        }
        
        .testimonials h2 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .testimonial-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        
        .testimonial {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .testimonial-text {
            font-style: italic;
            margin-bottom: 15px;
        }
        
        .testimonial-author {
            font-weight: bold;
            color: #8B4513;
            text-align: right;
        }
        
        /* Footer */
        footer {
            background-color: #704214;
            color: #F4ECD8;
            padding: 40px 0 20px;
            margin-top: 40px;
        }
        
        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .footer-column {
            width: 30%;
            min-width: 250px;
            margin-bottom: 20px;
        }
        
        .footer-column h3 {
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: #FF7F50;
        }
        
        .footer-column ul {
            list-style: none;
        }
        
        .footer-column ul li {
            margin-bottom: 10px;
        }
        
        .footer-column ul li a {
            color: #F4ECD8;
            text-decoration: none;
        }
        
        .footer-column ul li a:hover {
            color: #FF7F50;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            margin-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            header {
                padding: 10px 0;
            }
            
            nav {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                width: 100%;
                margin-top: 15px;
            }
            
            nav a {
                margin: 5px;
                padding: 8px 12px;
                font-size: 0.9rem;
            }
            
            .donation-cards {
                flex-direction: column;
                align-items: center;
            }
            
            .donation-card {
                width: 100%;
                max-width: 300px;
            }
            
            .donation-form-container {
                padding: 20px 15px;
            }
            
            .amount-options {
                gap: 5px;
            }
            
            .amount-option {
                min-width: 60px;
            }
            
            .footer-column {
                width: 100%;
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <a href="index.html" class="logo">
                <img src="bmmq6xrkp3.png" alt="Leola's Library Logo">
            </a>
            
            <nav>
                <a href="index.html">Home</a>
                <a href="index.html#books">Books</a>
                <a href="index.html#games">Games</a>
                <a href="index.html#about">About Leola</a>
                <a href="index.html#resources">Resources</a>
                <a href="index.html#tutorials">Tutorials</a>
                <a href="index.html#faq">FAQ</a>
                <a href="index.html#contact">Contact</a>
                <a href="donations.html" class="active">Donate</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <h1>Support Leola's Crochet World</h1>
            
            <p style="text-align: center; max-width: 800px; margin: 0 auto 40px; font-size: 1.1rem;">
                Your generous donations help Sister Lee continue creating educational crochet content, maintaining this website, and sharing the art of crochet with our community. Every contribution, no matter the size, makes a difference in our mission to preserve and pass on this beautiful craft.
            </p>
            
            <div class="donation-cards">
                <div class="donation-card">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5-1.95 0-4.05.4-5.5 1.5v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z'/%3E%3C/svg%3E" alt="Educational Materials Icon">
                    <h3>Educational Materials</h3>
                    <p>Your donation helps create new books, tutorials, and instructional materials for crocheters of all levels.</p>
                </div>
                
                <div class="donation-card">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.17-5.24l-1.1-1.1c.71-1.33.53-3.01-.59-4.13C13.79 8.84 12.9 8.5 12 8.5c-.03 0-.06.01-.09.01L13 9.6l-1.06 1.06-2.83-2.83L11.94 5 13 6.06l-.96.94c1.27-.01 2.54.45 3.54 1.46 1.71 1.71 1.92 4.35.59 6.3z'/%3E%3C/svg%3E" alt="Community Workshops Icon">
                    <h3>Community Workshops</h3>
                    <p>Support free community workshops and crochet classes for those who can't afford traditional instruction.</p>
                </div>
                
                <div class="donation-card">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7F50'%3E%3Cpath d='M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z'/%3E%3C/svg%3E" alt="Website Maintenance Icon">
                    <h3>Website Maintenance</h3>
                    <p>Help keep our digital resources accessible, up-to-date, and free for everyone to use.</p>
                </div>
            </div>
            
            <div class="donation-form-container">
                <h2>Make a Donation</h2>
                <form id="donation-form" action="#" method="post">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Select Donation Amount</label>
                        <div class="amount-options">
                            <div class="amount-option">
                                <input type="radio" id="amount-10" name="amount" value="10" checked>
                                <label for="amount-10">$10</label>
                            </div>
                            
                            <div class="amount-option">
                                <input type="radio" id="amount-25" name="amount" value="25">
                                <label for="amount-25">$25</label>
                            </div>
                            
                            <div class="amount-option">
                                <input type="radio" id="amount-50" name="amount" value="50">
                                <label for="amount-50">$50</label>
                            </div>
                            
                            <div class="amount-option">
                                <input type="radio" id="amount-100" name="amount" value="100">
                                <label for="amount-100">$100</label>
                            </div>
                            
                            <div class="amount-option">
                                <input type="radio" id="amount-custom" name="amount" value="custom">
                                <label for="amount-custom">Other</label>
                            </div>
                        </div>
                        
                        <div class="form-group" id="custom-amount-container" style="display: none;">
                            <label for="custom-amount">Custom Amount ($)</label>
                            <input type="number" id="custom-amount" name="custom-amount" placeholder="Enter amount" min="1">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Message (Optional)</label>
                        <textarea id="message" name="message" rows="4"></textarea>
                    </div>
                    
                    <button type="submit" id="donate-button">Donate Now</button>
                </form>
                
                <div class="stripe-container">
                    <p>Or donate quickly with Stripe:</p>
                    <a href="https://buy.stripe.com/7sI0282DR9075u87sw" target="_blank" rel="noopener noreferrer" class="stripe-button" id="stripe-link">
                        🎁 Donate to Support Our Work
                    </a>
                </div>
            </div>
            
            <div class="testimonials">
                <h2>What Our Supporters Say</h2>
                <div class="testimonial-grid">
                    <div class="testimonial">
                        <div class="testimonial-text">
                            Sister Lee's crochet lessons changed my life. Supporting her work is my way of saying thank you for all she's given to our community.
                        </div>
                        <div class="testimonial-author">
                            - Maria Johnson, Supporter since 2020
                        </div>
                    </div>
                    
                    <div class="testimonial">
                        <div class="testimonial-text">
                            When I donate to Leola's Crochet World, I know I'm helping preserve traditional craft skills and passing them on to the next generation.
                        </div>
                        <div class="testimonial-author">
                            - Thomas Wright, Monthly Donor
                        </div>
                    </div>
                    
                    <div class="testimonial">
                        <div class="testimonial-text">
                            The free resources here helped me learn crochet during a difficult time. Now that I can, I give back so others can have the same opportunity.
                        </div>
                        <div class="testimonial-author">
                            - Aisha Patel, Community Member
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>About Leola's Library</h3>
                    <p>A warm and welcoming space dedicated to sharing the joy of crochet through stories, tutorials, and community support.</p>
                </div>
                
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#books">Books</a></li>
                        <li><a href="index.html#games">Games</a></li>
                        <li><a href="index.html#tutorials">Tutorials</a></li>
                        <li><a href="donations.html">Support Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>Contact Sister Lee</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: (*************</li>
                        <li>Milwaukee, WI</li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 Leola's Crochet World. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/agent-lee-final.js"></script>
    <script>
      // Force Agent Lee to appear and speak on donations page
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Donations page loaded, ensuring Agent Lee appears...');
        
        // FIRST ATTEMPT: Force create Agent Lee card
        function createAgentLeeCardWithRetry(retryCount = 0) {
          console.log('Forcing Agent Lee card creation (attempt ' + (retryCount + 1) + ')...');
          
          if (document.getElementById('agent-lee-card')) {
            console.log('Agent Lee card already exists, no need to create');
            return;
          }
          
          if (typeof createAgentLeeCard === 'function') {
            createAgentLeeCard();
            console.log('Agent Lee card created successfully');
          } else if (typeof window.createAgentLeeCard === 'function') {
            window.createAgentLeeCard();
            console.log('Agent Lee card created successfully via window object');
          } else if (retryCount < 5) {
            console.warn('createAgentLeeCard function not found yet, retrying in 1s... (attempt ' + (retryCount + 1) + ')');
            setTimeout(function() {
              createAgentLeeCardWithRetry(retryCount + 1);
            }, 1000);
          } else {
            console.error('CRITICAL: Failed to create Agent Lee card after multiple attempts!');
            
            // Last resort: Create card manually
            console.log('Attempting to create card manually...');
            const agentLeeCard = document.createElement('div');
            agentLeeCard.id = 'agent-lee-card';
            agentLeeCard.className = 'expanded';
            agentLeeCard.innerHTML = `
              <div class="card-header">
                <div class="avatar">
                  <img src="xhabe2hpi5.png" alt="Agent Lee">
                </div>
                <div class="agent-details">
                  <h3>Agent Lee</h3>
                  <p>Leola's Helpful Librarian</p>
                </div>
              </div>
              <div class="chat-area">
                <div class="chat-messages" id="chat-messages">
                  <div class="message agent-message">
                    Thank you for visiting our donations page! Your support helps Sister Leola continue her work.
                  </div>
                </div>
              </div>
            `;
            document.body.appendChild(agentLeeCard);
          }
        }
        
        // Start the creation process
        setTimeout(createAgentLeeCardWithRetry, 1000);
        
        // IMPORTANT: Force Agent Lee to speak - with retries
        function speakDonationsIntroWithRetry(retryCount = 0) {
          console.log('Forcing Agent Lee to speak donation introduction (attempt ' + (retryCount + 1) + ')...');
          
          if (window.speakAgentLeeSection) {
            window.speakAgentLeeSection('donations');
          } else if (window.speakText) {
            const donationMessage = "Thank you for considering supporting Sister Leola's Library! Your generous donations help maintain this digital library and fund the creation of new books, tutorials, and interactive features. All contributions directly support Sister Leola's mission to make crochet instruction accessible to everyone.";
            window.speakText(donationMessage);
          } else if (retryCount < 5) {
            console.warn('Speech functions not found yet, retrying in 1s... (attempt ' + (retryCount + 1) + ')');
            setTimeout(function() {
              speakDonationsIntroWithRetry(retryCount + 1);
            }, 1000);
          } else {
            console.error('CRITICAL: Failed to make Agent Lee speak after multiple attempts!');
          }
        }
        
        // Start the speech process
        setTimeout(function() {
          speakDonationsIntroWithRetry();
        }, 2500);
      });
      
      // Additional fallback - continuously check for Agent Lee card
      setInterval(function() {
        if (!document.getElementById('agent-lee-card') && typeof createAgentLeeCard === 'function') {
          console.log('FALLBACK: Agent Lee card missing, recreating...');
          createAgentLeeCard();
        }
      }, 5000);
    </script>
    <script>
        // Agent Lee welcome message for donations page
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for Agent Lee to initialize
            setTimeout(function() {
                if (window.speakText) {
                    const donationMessage = "Welcome to our donations page! Your support helps Sister Lee continue creating educational content and maintaining this site for the crochet community. Even a small contribution makes a big difference. Thank you for considering a donation today!";
                    window.speakText(donationMessage);
                    
                    // Add message to Agent Lee's chat using the global function
                    if (window.addAgentLeeMessage) {
                        window.addAgentLeeMessage(donationMessage, 'agent');
                    }
                }
            }, 2000);
        });
        
        // Show/hide custom amount input
        document.querySelectorAll('input[name="amount"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const customAmountContainer = document.getElementById('custom-amount-container');
                customAmountContainer.style.display = this.value === 'custom' ? 'block' : 'none';
                
                if (this.value === 'custom') {
                    document.getElementById('custom-amount').focus();
                }
            });
        });
        
        // Handle form submission
        document.getElementById('donation-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            let amount = document.querySelector('input[name="amount"]:checked').value;
            
            // If custom amount is selected, use that value
            if (amount === 'custom') {
                amount = document.getElementById('custom-amount').value;
                // Default to 10 if no custom amount entered
                if (!amount || amount < 1) {
                    amount = 10;
                }
            }
            
            // Redirect to Stripe checkout
            window.location.href = 'https://buy.stripe.com/7sI0282DR9075u87sw';
        });
        
        // Direct Stripe button
        document.getElementById('stripe-link').addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = 'https://buy.stripe.com/7sI0282DR9075u87sw';
        });
    </script>
</body>
</html>