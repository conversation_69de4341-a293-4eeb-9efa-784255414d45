<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Crochet Adventures with Agent <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: 'Georgia', 'Times New Roman', Times, serif; /* Default to storybook font */
            background-color: #fdf6e3; /* Creamy background similar to NY book */
            color: #333;
            overscroll-behavior: none; /* Prevent pull-to-refresh issues */
        }
        .font-guide-active { /* Applied to body when Crochet Mastery book or dark theme is active */
             font-family: 'sans-serif';
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #fdf6e3; /* Light background for track */
        }
        ::-webkit-scrollbar-thumb {
            background: #d4a373; /* Earthy brown for thumb */
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #b38a5f; /* Darker brown on hover */
        }
        .glassmorphism {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
         .dark-glassmorphism {
            background: rgba(15, 23, 42, 0.6); /* Tailwind slate-900 with opacity */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(51, 65, 85, 0.5); /* Tailwind slate-700 with opacity */
        }
        [data-theme="dark"] body {
            background-color: #0f172a; /* Dark blue from CM guide */
            color: #f1f5f9;
            font-family: 'sans-serif'; /* Default to guide font in dark mode */
        }
        [data-theme="dark"] ::-webkit-scrollbar-track {
            background: #0f172a;
        }
        [data-theme="dark"] ::-webkit-scrollbar-thumb {
            background: #38bdf8; /* Light blue from CM guide h1 */
        }
        [data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
            background: #0ea5e9; 
        }
        /* Styles for rendered chapter content */
        .chapter-content h1, .chapter-content h2, .chapter-content h3, .chapter-content h4 {
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        .chapter-content h1 { font-size: 2em; /* Adjusted for consistency */ }
        .chapter-content h2 { font-size: 1.75em; border-bottom: 1px solid #ccc; padding-bottom: 5px;}
        .chapter-content h3 { font-size: 1.5em; }
        .chapter-content h4 { font-size: 1.25em; }
        .chapter-content p { margin-bottom: 1em; line-height: 1.7; }
        .chapter-content ul, .chapter-content ol { list-style: disc; margin-left: 20px; margin-bottom: 1em; }
        .chapter-content ol { list-style: decimal; }
        .chapter-content li { margin-bottom: 0.5em; }
        .chapter-content a { color: #8B4513; text-decoration: underline; }
        .chapter-content pre { background: #f4f4f4; color: #333; padding: 10px; border-radius: 5px; overflow-x: auto; margin-bottom: 1em; font-family: monospace; font-size: 0.9em;}
        .chapter-content img { max-width: 100%; height: auto; border-radius: 8px; margin: 1em 0; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .chapter-content strong { font-weight: bold; }
        .chapter-content em { font-style: italic; }
        .chapter-content blockquote { border-left: 4px solid #d4a373; padding-left: 1em; margin-left: 0; font-style: italic; color: #5a3d20; }
        
        /* Dark theme overrides for chapter content */
        [data-theme="dark"] .chapter-content h1, 
        [data-theme="dark"] .chapter-content h2, 
        [data-theme="dark"] .chapter-content h3,
        [data-theme="dark"] .chapter-content h4 { color: #38bdf8; }
        [data-theme="dark"] .chapter-content a { color: #7dd3fc; }
        [data-theme="dark"] .chapter-content pre { background: #1e293b; color: #e2e8f0; }
        [data-theme="dark"] .chapter-content h2 { border-bottom-color: #334155; }
        [data-theme="dark"] .chapter-content blockquote { border-left-color: #38bdf8; color: #94a3b8; }

        /* Theme specific color classes - will be generated by JS for Tailwind to pick up */
        /* .text-pink-500 .border-pink-500 .hover\:border-pink-400 .bg-pink-500 .hover\:bg-pink-600 .focus\:ring-pink-400 .border-pink-400 .text-pink-400 .bg-pink-600 */
        /* .text-sky-500 .border-sky-500 .hover\:border-sky-400 .bg-sky-500 .hover\:bg-sky-600 .focus\:ring-sky-400 .border-sky-400 .text-sky-400 .bg-sky-600 .text-sky-300 */
        /* .text-yellow-400 .border-yellow-400 .bg-yellow-600 */
        /* .text-slate-500 .border-slate-500 .bg-slate-600 .hover\:bg-slate-700 .focus\:ring-slate-500 .border-slate-600 .text-slate-300 .text-slate-400 */
        /* .ring-pink-500 .ring-sky-500 .bg-sky-800\/50 .border-sky-700 .text-pink-300 .bg-pink-800\/50 .border-pink-700*/
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback, createContext, useContext } = React;

        // --- Constants & Application Data ---
        const DB_NAME = 'CrochetAppDB_V2'; // Changed version to V2 if schema might change
        const DB_VERSION = 1;
        const STORE_PROGRESS = 'userProgress_v2';
        const STORE_SETTINGS = 'userSettings_v2';

        const APP_BOOKS = [
            {
                id: 'needleAndYarn',
                title: 'Needle & Yarn: A Love Stitched in Time',
                author: 'Leola (Sista) Lee',
                description: 'Embark on a heartwarming crochet adventure with Elara and her magical companions, Needle and Yarn. Discover the power of stitches to mend hearts and weave destinies in this enchanting tale of love, loss, and the threads that bind us.',
                coverImage: 'https://picsum.photos/seed/needleandyarn_book/400/600',
                themeColor: 'pink-500', // Tailwind color
                fontClass: '', // Default storybook font
                chapters: [
                    {
                        id: 'ny_prologue',
                        title: '🌟 Prologue: A Stitch in Time',
                        awardIdOnCompletion: 'award_ny_prologue',
                        content: [
                            { type: 'heading', level: 2, text: '🌟 Prologue: A Stitch in Time' },
                            { type: 'paragraph', text: '“Gather \'round, my dears, and let Sista Lee spin you a tale not of kings and queens, but of humble thread and gleaming hook, and of a love stitched into the very fabric of time.” The fire crackled, casting dancing shadows on the faces of the children, their eyes wide with anticipation. Sista Lee, her hands never still, a crochet hook dancing deftly through yarn, began her story...' },
                            { type: 'image', src: 'https://picsum.photos/seed/prologueimage/600/400', alt: 'Cozy fireplace storytelling' },
                            { type: 'paragraph', text: 'Our story begins in a small, sun-dappled cottage nestled beside a whispering willow tree, where a young woman named Elara found solace in the rhythmic dance of her crochet hook...' }
                        ],
                    },
                    {
                        id: 'ny_ch1',
                        title: 'Chapter 1: The Whispering Hook',
                        awardIdOnCompletion: 'award_ny_ch1',
                        content: [
                            { type: 'heading', level: 2, text: 'Chapter 1: The Whispering Hook' },
                            { type: 'paragraph', text: 'Elara held the cool, smooth crochet hook her grandmother had gifted her. It wasn’t just any hook; it seemed to hum with a life of its own...' },
                            { type: 'instructionHeader', title: 'Instructional Interlude: The Slip Knot' },
                            { type: 'instructionStep', stepNumber: 1, text: 'Make a loop with your yarn, crossing the tail end over the working yarn.' },
                            { type: 'instructionStep', stepNumber: 2, text: 'Insert your hook through the loop from front to back.' },
                            { type: 'instructionStep', stepNumber: 3, text: 'Yarn over (wrap the yarn around your hook).' },
                            { type: 'instructionStep', stepNumber: 4, text: 'Draw the yarn through the loop on your hook. You’ve made a slip knot!' },
                            { type: 'tip', text: 'Keep your tension even but not too tight. The knot should slide easily on the hook.' },
                        ],
                        quizId: 'quiz_ny_ch1_slipknot'
                    }
                ]
            },
            {
                id: 'mastery',
                title: 'Crochet Mastery: A Complete Guide',
                author: 'Leola (Sista) Lee',
                description: 'Unlock the secrets of crochet with this comprehensive guide by Sista Lee. From foundational stitches to advanced techniques, build your skills, master patterns, and create beautiful, handcrafted items with confidence.',
                coverImage: 'https://picsum.photos/seed/crochetmastery_book/400/500',
                themeColor: 'sky-500',
                fontClass: 'font-guide-active', // Sans-serif for guide
                chapters: [
                    {
                        id: 'cm_intro_tools',
                        title: 'Introduction: Tools & Materials',
                        awardIdOnCompletion: 'award_cm_intro',
                        content: [
                            { type: 'heading', level: 2, text: 'Welcome to Crochet Mastery!' },
                            { type: 'paragraph', text: 'This guide will walk you through everything you need to know to become a confident crocheter. Let\'s start with the absolute basics: your tools and materials.' },
                            { type: 'heading', level: 3, text: 'Crochet Hooks' },
                            { type: 'paragraph', text: 'Hooks come in various sizes and materials (aluminum, bamboo, plastic, steel). Aluminum hooks are great for beginners as they are smooth and affordable.' },
                            { type: 'image', src: 'https://picsum.photos/seed/crochethooks/500/300', alt: 'Various crochet hooks' },
                            { type: 'heading', level: 3, text: 'Yarn' },
                            { type: 'paragraph', text: 'Yarn is classified by weight (thickness), from lace weight (thinnest) to jumbo (thickest). Worsted weight (medium) is a popular choice for beginners.' },
                            { type: 'list', items: ['Cotton: Strong, good stitch definition.', 'Wool: Warm, elastic.', 'Acrylic: Affordable, wide color range, easy care.'] },
                            { type: 'quote', text: 'Sista Lee says: "The right yarn and hook make all the difference, sugar. Don\'t be afraid to experiment!"'}
                        ],
                        quizId: 'quiz_cm_tools'
                    },
                    {
                        id: 'cm_ch1_basic_stitches',
                        title: 'Chapter 1: Basic Stitches - Chain & Single Crochet',
                        awardIdOnCompletion: 'award_cm_ch1_stitches',
                        content: [
                            { type: 'heading', level: 2, text: 'Chapter 1: Basic Stitches' },
                            { type: 'instructionHeader', title: 'The Chain Stitch (ch)' },
                            { type: 'paragraph', text: 'The chain stitch is the foundation of most crochet projects.' },
                            { type: 'instructionStep', stepNumber: 1, text: 'Start with a slip knot on your hook.' },
                            { type: 'instructionStep', stepNumber: 2, text: 'Yarn over.' },
                            { type: 'instructionStep', stepNumber: 3, text: 'Draw the yarn through the loop on your hook. That’s one chain stitch!' },
                            { type: 'tip', text: 'Practice making a chain of even stitches. Count them as you go!' },
                            { type: 'instructionHeader', title: 'The Single Crochet Stitch (sc)' },
                             { type: 'paragraph', text: 'The single crochet is a short, dense stitch.' },
                            { type: 'instructionStep', stepNumber: 1, text: 'Insert hook into the indicated stitch (often the second chain from hook for a foundation row).' },
                            { type: 'instructionStep', stepNumber: 2, text: 'Yarn over and pull up a loop (2 loops on hook).' },
                            { type: 'instructionStep', stepNumber: 3, text: 'Yarn over and draw through both loops on hook. That’s one single crochet!' },
                        ],
                        quizId: 'quiz_cm_basic_stitches'
                    }
                ]
            }
        ];

        const APP_QUIZZES = [
            {
                id: 'quiz_ny_ch1_slipknot',
                bookId: 'needleAndYarn',
                title: 'Slip Knot Challenge',
                awardIdOnCompletion: 'award_quiz_ny_slipknot',
                questions: [
                    { id: 'q1', text: 'What is the first step in making a slip knot as described?', options: [{id: 'o1', text:'Yarn over'}, {id: 'o2', text:'Make a loop, crossing tail over working yarn'}, {id: 'o3', text:'Insert hook into working yarn'}], correctOptionId: 'o2' },
                    { id: 'q2', text: 'What should the tension be like for a slip knot?', options: [{id: 'o1', text:'Very tight'}, {id: 'o2', text:'Loose and wobbly'}, {id: 'o3', text:'Even, allowing easy sliding on hook'}], correctOptionId: 'o3' },
                ]
            },
            {
                id: 'quiz_cm_tools',
                bookId: 'mastery',
                title: 'Tools & Yarn Quiz',
                awardIdOnCompletion: 'award_quiz_cm_tools',
                questions: [
                    { id: 'q1', text: 'Which hook material is often recommended for beginners?', options: [{id: 'o1', text:'Steel'}, {id: 'o2', text:'Bamboo'}, {id: 'o3', text:'Aluminum'}], correctOptionId: 'o3' },
                    { id: 'q2', text: 'What does "worsted weight" refer to in yarn?', options: [{id: 'o1', text:'Color intensity'}, {id: 'o2', text:'Yarn thickness'}, {id: 'o3', text:'Fiber content percentage'}], correctOptionId: 'o2' },
                ]
            },
            {
                id: 'quiz_cm_basic_stitches',
                bookId: 'mastery',
                title: 'Basic Stitches Quiz',
                awardIdOnCompletion: 'award_quiz_cm_basic_stitches',
                questions: [
                    { id: 'q1', text: 'What is the foundation of most crochet projects?', options: [{id: 'o1', text:'Single Crochet'}, {id: 'o2', text:'Slip Knot'}, {id: 'o3', text:'Chain Stitch'}], correctOptionId: 'o3' },
                    { id: 'q2', text: 'How many loops are on the hook right BEFORE the final yarn over in a single crochet stitch?', options: [{id: 'o1', text:'One loop'}, {id: 'o2', text:'Two loops'}, {id: 'o3', text:'Three loops'}], correctOptionId: 'o2' },
                ]
            }
        ];

        const APP_AWARDS = [
            { id: 'award_welcome', name: 'Welcome Crocheter!', description: 'Started your crochet adventure.', icon: '🎉', earned: false, bookId: null },
            { id: 'award_ny_prologue', name: 'Story Listener', description: 'Read the Prologue of Needle & Yarn.', icon: '📖', earned: false, bookId: 'needleAndYarn' },
            { id: 'award_ny_ch1', name: 'Hook Whisperer', description: 'Completed Chapter 1 of Needle & Yarn.', icon: '🧚', earned: false, bookId: 'needleAndYarn' },
            { id: 'award_quiz_ny_slipknot', name: 'Knot Navigator', description: 'Passed the Slip Knot Challenge!', icon: '🪢', earned: false, bookId: 'needleAndYarn' },
            { id: 'award_cm_intro', name: 'Tool Explorer', description: 'Learned about Crochet Tools & Materials.', icon: '🛠️', earned: false, bookId: 'mastery' },
            { id: 'award_quiz_cm_tools', name: 'Tool Quiz Whiz', description: 'Passed the Tools & Yarn quiz!', icon: '🏅', earned: false, bookId: 'mastery' },
            { id: 'award_cm_ch1_stitches', name: 'Stitch Starter', description: 'Learned Basic Stitches in Crochet Mastery.', icon: '🧶', earned: false, bookId: 'mastery' },
            { id: 'award_quiz_cm_basic_stitches', name: 'Stitch Master', description: 'Passed the Basic Stitches quiz!', icon: '🏆', earned: false, bookId: 'mastery' },
            { id: 'award_all_ny', name: 'Story Weaver', description: 'Completed all Needle & Yarn chapters!', icon: '📜', earned: false, bookId: 'needleAndYarn' },
            { id: 'award_all_mastery', name: 'Crochet Guru', description: 'Completed all Crochet Mastery chapters!', icon: '🎓', earned: false, bookId: 'mastery' },
        ];
        
        const getBookById = (bookId) => APP_BOOKS.find(b => b.id === bookId);
        const getQuizById = (quizId) => APP_QUIZZES.find(q => q.id === quizId);
        const getAwardById = (awardId) => APP_AWARDS.find(a => a.id === awardId);
        const getChapterById = (bookId, chapterId) => {
            const book = getBookById(bookId);
            return book ? book.chapters.find(c => c.id === chapterId) : null;
        };


        // --- SVG Icons --- (Copied from original prompt's index.html)
        const IconPlay = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path fillRule="evenodd" d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z" clipRule="evenodd" /></svg>;
        const IconPause = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path fillRule="evenodd" d="M6.75 5.25a.75.75 0 0 1 .75.75V18a.75.75 0 0 1-1.5 0V6a.75.75 0 0 1 .75-.75Zm10.5 0a.75.75 0 0 1 .75.75V18a.75.75 0 0 1-1.5 0V6a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" /></svg>;
        const IconVolumeUp = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path d="M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 0 0 1.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.66 1.905H6.44l4.5 4.5c.945.945 2.56.276 2.56-1.06V4.06ZM18.584 5.106a.75.75 0 0 1 1.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 0 1-1.06-1.06 8.25 8.25 0 0 0 0-11.668.75.75 0 0 1 0-1.06Z" /><path d="M15.932 7.757a.75.75 0 0 1 1.061 0 6 6 0 0 1 0 8.486.75.75 0 0 1-1.06-1.061 4.5 4.5 0 0 0 0-6.364.75.75 0 0 1 0-1.06Z" /></svg>;
        const IconVolumeOff = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path d="M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 0 0 1.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.66 1.905H6.44l4.5 4.5c.945.945 2.56.276 2.56-1.06V4.06ZM17.28 9.53a.75.75 0 1 0-1.06-1.06L15 9.69l-1.22-1.22a.75.75 0 0 0-1.06 1.06L13.94 10.75l-1.22 1.22a.75.75 0 1 0 1.06 1.06L15 11.81l1.22 1.22a.75.75 0 1 0 1.06-1.06L16.06 10.75l1.22-1.22Z" /></svg>;
        const IconBookOpen = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path d="M11.25 4.533A9.707 9.707 0 0 0 6 3a9.735 9.735 0 0 0-3.25.555.75.75 0 0 0-.5.707v14.509a.75.75 0 0 0 .5.707A9.735 9.735 0 0 0 6 21a9.707 9.707 0 0 0 5.25-1.533A9.707 9.707 0 0 0 18 21a9.735 9.735 0 0 0 3.25-.555.75.75 0 0 0 .5-.707V3.555a.75.75 0 0 0-.5-.707A9.735 9.735 0 0 0 18 3a9.707 9.707 0 0 0-5.25 1.533ZM12.75 6.012A8.209 8.209 0 0 0 18 4.5a8.209 8.209 0 0 0-5.25 1.512A8.209 8.209 0 0 0 7.5 4.5a8.209 8.209 0 0 0 5.25 1.512Z" /></svg>;
        const IconTrophy = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path fillRule="evenodd" d="M4.5 3.75a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V6.75a3 3 0 0 0-3-3h-15Zm4.125 3.375c0 .621.504 1.125 1.125 1.125h3a1.125 1.125 0 0 0 1.125-1.125V6.375c0-.621-.504-1.125-1.125-1.125h-3A1.125 1.125 0 0 0 8.625 6.375v.75Z" clipRule="evenodd" /></svg>;
        const IconArrowLeft = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path fillRule="evenodd" d="M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z" clipRule="evenodd" /></svg>;
        const IconCheckCircle = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-green-500"><path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.06-1.06l-3.002 3.001-1.502-1.502a.75.75 0 0 0-1.06 1.061l2.252 2.252a.75.75 0 0 0 1.06 0l3.75-3.75Z" clipRule="evenodd" /></svg>;
        const IconLightBulb = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path d="M12 2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM7.5 12a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM18.894 6.166a.75.75 0 0 0-1.06-1.06l-1.591 1.59a.75.75 0 1 0 1.06 1.061l1.591-1.59ZM21.75 12a.75.75 0 0 1-.75.75h-2.25a.75.75 0 0 1 0-1.5H21a.75.75 0 0 1 .75.75ZM17.834 18.894a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 1 0-1.061 1.06l1.59 1.591ZM12 18a.75.75 0 0 1 .75.75V21a.75.75 0 0 1-1.5 0v-2.25A.75.75 0 0 1 12 18ZM7.758 17.303a.75.75 0 0 0-1.061-1.06l-1.591 1.59a.75.75 0 0 0 1.06 1.061l1.591-1.59ZM6 12a.75.75 0 0 1-.75.75H3a.75.75 0 0 1 0-1.5h2.25A.75.75 0 0 1 6 12ZM6.166 7.758a.75.75 0 0 0-1.06 1.06l1.59 1.591a.75.75 0 0 0 1.061-1.06l-1.59-1.591Z" /></svg>;
        const IconMoon = () => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"><path fillRule="evenodd" d="M9.528 1.718a.75.75 0 0 1 .162.819A8.97 8.97 0 0 0 9 6a9 9 0 0 0 9 9 8.97 8.97 0 0 0 3.463-.69.75.75 0 0 1 .981.98 10.503 10.503 0 0 1-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-3.51 1.713-6.636 4.362-8.492Z" clipRule="evenodd" /></svg>;

        // --- IndexedDB Service --- (Copied from original prompt's index.html)
        const initDB = () => {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(STORE_PROGRESS)) {
                        // Store user progress as a single object with multiple sets
                        const progressStore = db.createObjectStore(STORE_PROGRESS, { keyPath: 'id' });
                        progressStore.createIndex('id', 'id', { unique: true });
                    }
                    if (!db.objectStoreNames.contains(STORE_SETTINGS)) {
                         const settingsStore = db.createObjectStore(STORE_SETTINGS, { keyPath: 'id' });
                         settingsStore.createIndex('id', 'id', { unique: true });
                    }
                };
                request.onsuccess = (event) => resolve(event.target.result);
                request.onerror = (event) => reject(event.target.error);
            });
        };

        const getFromDB = (db, storeName, key) => {
            return new Promise((resolve, reject) => {
                if (!db) return reject("DB not initialized");
                const transaction = db.transaction(storeName, 'readonly');
                const store = transaction.objectStore(storeName);
                const request = store.get(key);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        };

        const saveToDB = (db, storeName, data) => {
            return new Promise((resolve, reject) => {
                if (!db) return reject("DB not initialized");
                const transaction = db.transaction(storeName, 'readwrite');
                const store = transaction.objectStore(storeName);
                const request = store.put(data);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        };
        
        // --- Context for global state and actions ---
        const AppContext = createContext();

        const AppProvider = ({ children }) => {
            const [db, setDb] = useState(null);
            // Progress state
            const [userProgress, setUserProgress] = useState({
                readChapters: new Set(), // "bookId-chapterId"
                completedQuizzes: new Set(), // quizId
                earnedAwards: new Set() // awardId
            });
            const [awardsData, setAwardsData] = useState(APP_AWARDS.map(a => ({...a, earned: false})));


            // App state
            const [currentView, setCurrentView] = useState('home'); // home, bookView, chapterView, quizView, awardsView
            const [selectedBookId, setSelectedBookId] = useState(null);
            const [selectedChapterId, setSelectedChapterId] = useState(null);
            const [selectedQuizId, setSelectedQuizId] = useState(null);
            
            // UI/UX state (from original index.html)
            const [narratorText, setNarratorText] = useState('');
            const [isNarrating, setIsNarrating] = useState(false);
            const [voiceEnabled, setVoiceEnabled] = useState(true);
            const [theme, setTheme] = useState('light'); // 'light' or 'dark'
            const [showWelcome, setShowWelcome] = useState(false);

            // Initialize DB and load data
            useEffect(() => {
                initDB().then(database => {
                    setDb(database);
                    // Load settings
                    getFromDB(database, STORE_SETTINGS, 'appSettings').then(settings => {
                        if (settings) {
                            setVoiceEnabled(settings.voiceEnabled !== undefined ? settings.voiceEnabled : true);
                            setTheme(settings.theme || 'light');
                            setShowWelcome(settings.showWelcome !== undefined ? settings.showWelcome : true);
                        } else {
                           setShowWelcome(true); // First time
                           addAwardInternal('award_welcome', true); // Award welcome badge immediately
                        }
                    });
                    // Load progress
                    getFromDB(database, STORE_PROGRESS, 'mainProgress').then(savedProgress => {
                       if (savedProgress) {
                           setUserProgress({
                               readChapters: new Set(savedProgress.readChapters || []),
                               completedQuizzes: new Set(savedProgress.completedQuizzes || []),
                               earnedAwards: new Set(savedProgress.earnedAwards || [])
                           });
                           setAwardsData(prevAwards => prevAwards.map(award => ({
                               ...award,
                               earned: (savedProgress.earnedAwards || []).includes(award.id)
                           })));
                       }
                    });
                }).catch(console.error);
            }, []);
            
            // Apply theme & font
            useEffect(() => {
                document.documentElement.setAttribute('data-theme', theme);
                const currentBook = getBookById(selectedBookId);
                if (theme === 'dark' || (currentBook && currentBook.fontClass)) {
                    document.body.classList.add(currentBook?.fontClass || 'font-guide-active');
                } else {
                    document.body.classList.remove('font-guide-active');
                     APP_BOOKS.forEach(b => { // remove any book specific font class
                        if(b.fontClass) document.body.classList.remove(b.fontClass);
                     });
                }
            }, [theme, selectedBookId]);

            // Save progress/settings whenever they change
            const throttledSaveProgress = useCallback(
                debounce((currentProgress) => {
                    if (db) {
                        saveToDB(db, STORE_PROGRESS, {
                            id: 'mainProgress',
                            readChapters: Array.from(currentProgress.readChapters),
                            completedQuizzes: Array.from(currentProgress.completedQuizzes),
                            earnedAwards: Array.from(currentProgress.earnedAwards)
                        }).catch(console.error);
                    }
                }, 1000), [db]);

            useEffect(() => {
                throttledSaveProgress(userProgress);
            }, [userProgress, throttledSaveProgress]);
            
            useEffect(() => {
                if (db) {
                    saveToDB(db, STORE_SETTINGS, {id: 'appSettings', voiceEnabled, theme, showWelcome }).catch(console.error);
                }
            }, [voiceEnabled, theme, showWelcome, db]);

            // Debounce utility
            function debounce(func, delay) {
                let timeout;
                return function(...args) {
                    const context = this;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(context, args), delay);
                };
            }

            const addAwardInternal = (awardId, fromInitialWelcome = false) => {
                let newBadgeAwarded = false;
                setUserProgress(prev => {
                    if (prev.earnedAwards.has(awardId)) return prev;
                    const newAwardsSet = new Set(prev.earnedAwards);
                    newAwardsSet.add(awardId);
                    newBadgeAwarded = true;
                    return { ...prev, earnedAwards: newAwardsSet };
                });
                setAwardsData(prevAwards => prevAwards.map(b => b.id === awardId ? { ...b, earned: true } : b));
                
                if (newBadgeAwarded && !fromInitialWelcome) {
                    const award = getAwardById(awardId);
                    const msg = `Great job! You've earned the "${award?.name}" award! Check your awards collection.`;
                    setNarratorText(msg);
                    if(voiceEnabled) speakAgentLeeText(msg);
                }
            };

            const markChapterAsRead = (bookId, chapterId) => {
                const fullChapterId = `${bookId}-${chapterId}`;
                let chapterAwardId = null;
                
                setUserProgress(prev => {
                    if (prev.readChapters.has(fullChapterId)) return prev;
                    const newReadChapters = new Set(prev.readChapters);
                    newReadChapters.add(fullChapterId);
                    return { ...prev, readChapters: newReadChapters };
                });

                const chapter = getChapterById(bookId, chapterId);
                if (chapter && chapter.awardIdOnCompletion) {
                    addAwardInternal(chapter.awardIdOnCompletion);
                    chapterAwardId = chapter.awardIdOnCompletion;
                }
                
                // Check for book completion award
                const book = getBookById(bookId);
                if (book) {
                    const allBookChapters = book.chapters.map(c => `${book.id}-${c.id}`);
                    const bookChaptersRead = allBookChapters.filter(fcId => userProgress.readChapters.has(fcId) || fcId === fullChapterId).length;

                    if (bookChaptersRead === allBookChapters.length) {
                        if (bookId === 'needleAndYarn' && !userProgress.earnedAwards.has('award_all_ny')) {
                            addAwardInternal('award_all_ny');
                        } else if (bookId === 'mastery' && !userProgress.earnedAwards.has('award_all_mastery')) {
                            addAwardInternal('award_all_mastery');
                        }
                    }
                }
                if (!chapterAwardId) { // Only narrate general completion if no specific award was just given
                    setNarratorText(`Chapter "${chapter?.title}" marked as read. Well done!`);
                    if(voiceEnabled) speakAgentLeeText(`Chapter "${chapter?.title}" marked as read. Well done!`);
                }
            };
            
            const processQuizCompletion = (quizId, score, totalQuestions) => {
                setUserProgress(prev => {
                    const newCompletedQuizzes = new Set(prev.completedQuizzes);
                    newCompletedQuizzes.add(quizId);
                    return { ...prev, completedQuizzes: newCompletedQuizzes };
                });

                const quiz = getQuizById(quizId);
                let awardedQuizSpecificAward = false;
                if (quiz && quiz.awardIdOnCompletion && score >= totalQuestions / 2) { // Pass if >= 50%
                    addAwardInternal(quiz.awardIdOnCompletion);
                    awardedQuizSpecificAward = true;
                }
                
                setCurrentView('chapterView'); // Go back to chapter or book
                if (awardedQuizSpecificAward) {
                    // Narration handled by addAwardInternal
                } else if (score >= totalQuestions / 2) {
                    setNarratorText(`Quiz completed! Well done! Your score: ${score}/${totalQuestions}.`);
                    if(voiceEnabled) speakAgentLeeText(`Quiz completed! Well done! Your score: ${score}/${totalQuestions}.`);
                } else {
                     setNarratorText(`Quiz completed. Your score: ${score}/${totalQuestions}. You can try again later to improve!`);
                    if(voiceEnabled) speakAgentLeeText(`Quiz completed. Your score: ${score}/${totalQuestions}. You can try again later to improve!`);
                }
            };

            const speakAgentLeeText = (text, onEndCallback) => { // From original index.html
                if (!voiceEnabled || !text) {
                    if(onEndCallback) onEndCallback();
                    return;
                }
                speechSynthesis.cancel(); 
                const utterance = new SpeechSynthesisUtterance(text);
                const voices = speechSynthesis.getVoices();
                let leolaVoice = voices.find(voice => voice.name.includes('Female') && voice.lang.startsWith('en'));
                 if (!leolaVoice) { 
                    leolaVoice = voices.find(voice => voice.lang.startsWith('en'));
                }
                if(leolaVoice) utterance.voice = leolaVoice;
                
                utterance.pitch = 1.1; 
                utterance.rate = 0.9; 
                utterance.onstart = () => setIsNarrating(true);
                utterance.onend = () => {
                    setIsNarrating(false);
                    if (onEndCallback) onEndCallback();
                };
                utterance.onerror = (e) => {
                     setIsNarrating(false);
                     console.error("Speech synthesis error", e);
                     if (onEndCallback) onEndCallback();
                };
                speechSynthesis.speak(utterance);
            };
            
            const handleSelectBook = (bookId) => {
                setSelectedBookId(bookId);
                setCurrentView('bookView');
                const book = getBookById(bookId);
                const narrationStartText = `Exploring ${book.title}. Select a chapter to begin.`;
                setNarratorText(narrationStartText);
                if(voiceEnabled) speakAgentLeeText(narrationStartText);
            };
            
            const handleSelectChapter = (bookId, chapterId) => {
                setSelectedBookId(bookId); // Ensure book context is right
                setSelectedChapterId(chapterId);
                setCurrentView('chapterView');
                const chapter = getChapterById(bookId, chapterId);
                const narrationStartText = `Let's dive into ${chapter.title}.`;
                setNarratorText(narrationStartText);
                if(voiceEnabled) speakAgentLeeText(narrationStartText);
                 // Mark as read when opened (if not already)
                if (!userProgress.readChapters.has(`${bookId}-${chapterId}`)) {
                    markChapterAsRead(bookId, chapterId);
                }
            };

            const handleStartQuiz = (quizId) => {
                const quiz = getQuizById(quizId);
                if (quiz) {
                    setSelectedQuizId(quizId);
                    setCurrentView('quizView');
                    const narrationStartText = `Time for the ${quiz.title}! Good luck!`;
                    setNarratorText(narrationStartText);
                     if(voiceEnabled) speakAgentLeeText(narrationStartText);
                }
            };
            
            const toggleTheme = () => { // From original index.html
                setTheme(prev => prev === 'light' ? 'dark' : 'light');
            };

            const resetAllProgress = () => {
                const defaultProgress = {
                    readChapters: new Set(),
                    completedQuizzes: new Set(),
                    earnedAwards: new Set(['award_welcome']) // Keep welcome award or re-award
                };
                setUserProgress(defaultProgress);
                setAwardsData(APP_AWARDS.map(a => ({...a, earned: a.id === 'award_welcome'})));
                if (db) {
                     saveToDB(db, STORE_PROGRESS, {
                        id: 'mainProgress',
                        readChapters: [],
                        completedQuizzes: [],
                        earnedAwards: ['award_welcome']
                    }).catch(console.error);
                }
                setCurrentView('home');
                setSelectedBookId(null);
                setSelectedChapterId(null);
                setSelectedQuizId(null);
                setNarratorText("All progress has been reset. Let's start fresh!");
                if(voiceEnabled) speakAgentLeeText("All progress has been reset. Let's start fresh!");
            };


            const value = {
                // Data
                books: APP_BOOKS,
                quizzes: APP_QUIZZES,
                awards: awardsData, // Reactive awards data
                // State
                currentView, setCurrentView,
                selectedBookId, setSelectedBookId,
                selectedChapterId, setSelectedChapterId,
                selectedQuizId, setSelectedQuizId,
                userProgress,
                // Actions
                handleSelectBook, handleSelectChapter, handleStartQuiz,
                markChapterAsRead, processQuizCompletion, addAward: addAwardInternal, // addAward for external use if needed
                resetAllProgress,
                // UI/UX
                narratorText, setNarratorText, isNarrating, setIsNarrating, voiceEnabled, setVoiceEnabled,
                speakAgentLeeText, theme, toggleTheme, showWelcome, setShowWelcome
            };

            return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
        };
        
        // --- Shared Components ---
        const PageContainer = ({ children, title, className = '' }) => {
            const { theme } = useContext(AppContext);
            const titleColor = theme === 'dark' ? 'text-sky-400' : 'text-[#8B4513]';
            return (
                <div className={`container mx-auto px-4 sm:px-6 lg:px-8 py-8 ${className}`}>
                {title && (
                    <h1 className={`text-4xl font-bold ${titleColor} mb-8 text-center sm:text-left`}>
                    {title}
                    </h1>
                )}
                {children}
                </div>
            );
        };

        const StyledButton = ({ children, variant = 'primary', size = 'md', className = '', bookThemeColor, ...props }) => {
            const baseStyles = 'font-semibold rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 transition-colors duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed';

            const sizeStyles = {
                sm: 'px-3 py-1.5 text-sm',
                md: 'px-5 py-2.5 text-base',
                lg: 'px-6 py-3 text-lg',
            };
            
            // Default theme colors
            let primaryBg = 'bg-sky-500 hover:bg-sky-600 focus:ring-sky-400';
            let secondaryBg = 'bg-pink-500 hover:bg-pink-600 focus:ring-pink-400';

            if (bookThemeColor) { // bookThemeColor is like 'pink-500' or 'sky-500'
                const colorName = bookThemeColor.split('-')[0]; // pink, sky
                primaryBg = `bg-${colorName}-500 hover:bg-${colorName}-600 focus:ring-${colorName}-400`;
                // Secondary can be an alternative or default if not specified
            }
            
            const variantStyles = {
                primary: `${primaryBg} text-white`,
                secondary: `${secondaryBg} text-white`,
                danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
                ghost: 'bg-transparent hover:bg-slate-700 text-slate-100 focus:ring-slate-500 border border-slate-600',
            };
             const { theme } = useContext(AppContext);
             if (theme === 'light') {
                variantStyles.ghost = 'bg-transparent hover:bg-gray-200 text-slate-700 focus:ring-gray-500 border border-gray-400';
             }


            return (
                <button
                className={`${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${className}`}
                {...props}
                >
                {children}
                </button>
            );
        };
        
        // --- Specific Components from Vite App (converted and adapted) ---
        const ContentRenderer = ({ content, bookThemeColor }) => {
            const { theme } = useContext(AppContext);
            const headingSizeMap = { 1: 'text-3xl', 2: 'text-2xl', 3: 'text-xl', 4: 'text-lg' };
            const defaultHeadingColor = theme === 'dark' ? `text-${bookThemeColor ? bookThemeColor.split('-')[0] + '-300' : 'sky-300'}` : `text-${bookThemeColor || 'gray-700'}`;
            
            return (
                <div className={`space-y-4 text-lg leading-relaxed ${theme === 'dark' ? 'text-slate-200' : 'text-slate-700'} chapter-content`}>
                {content.map((block, index) => {
                    const key = `content-block-${index}`;
                    switch (block.type) {
                    case 'heading':
                        const Tag = `h${block.level || 2}`;
                        return <Tag key={key} className={`${headingSizeMap[block.level || 2]} font-semibold ${defaultHeadingColor} mt-6 mb-3`}>{block.text}</Tag>;
                    case 'paragraph':
                        return <p key={key} dangerouslySetInnerHTML={{ __html: block.text }}></p>; // Allow basic HTML like <strong><em>
                    case 'list':
                        const ListTag = block.ordered ? 'ol' : 'ul';
                        return (
                        <ListTag key={key} className={`list-inside space-y-1 pl-4 ${block.ordered ? 'list-decimal' : 'list-disc'}`}>
                            {block.items?.map((item, itemIndex) => <li key={`${key}-item-${itemIndex}`} dangerouslySetInnerHTML={{ __html: item }}></li>)}
                        </ListTag>
                        );
                    case 'image':
                        return <img key={key} src={block.src} alt={block.alt || 'Content image'} className="my-4 rounded-lg shadow-md max-w-full mx-auto" style={{ maxHeight: '400px' }} />;
                    case 'quote':
                        return <blockquote key={key} className={`border-l-4 pl-4 py-2 italic my-4 ${theme === 'dark' ? `border-${bookThemeColor ? bookThemeColor.split('-')[0] + '-500' : 'pink-500'} text-${bookThemeColor ? bookThemeColor.split('-')[0] + '-300' : 'pink-300'}` : `border-${bookThemeColor || 'pink-500'} text-${bookThemeColor ? bookThemeColor.split('-')[0] + '-700' : 'pink-700'}`}`}>{block.text}</blockquote>;
                    case 'tip':
                        return <div key={key} className={`p-4 rounded-lg my-4 ${theme === 'dark' ? `bg-${bookThemeColor ? bookThemeColor.split('-')[0] + '-800' : 'sky-800'}/50 border border-${bookThemeColor ? bookThemeColor.split('-')[0] + '-700' : 'sky-700'}` : 'bg-yellow-50 border border-yellow-300'} `}><strong className={`${theme === 'dark' ? `text-${bookThemeColor ? bookThemeColor.split('-')[0] + '-400' : 'sky-400'}`: 'text-yellow-600'}`}>💡 Tip:</strong> {block.text}</div>;
                    case 'instructionHeader':
                        return <h3 key={key} className={`text-xl font-semibold mt-6 mb-3 ${theme === 'dark' ? `text-${bookThemeColor ? bookThemeColor.split('-')[0] + '-400' : 'pink-400'}` : `text-${bookThemeColor || 'pink-600'}`}`}>{block.title}</h3>;
                    case 'instructionStep':
                        return (
                        <div key={key} className="flex items-start space-x-3 my-3">
                            {block.stepNumber && <span className={`text-white rounded-full h-8 w-8 flex items-center justify-center font-bold flex-shrink-0 ${theme === 'dark' ? `bg-${bookThemeColor || 'pink-500'}` : `bg-${bookThemeColor || 'pink-500'}`}`}>{block.stepNumber}</span>}
                            <p className="flex-1" dangerouslySetInnerHTML={{ __html: block.text }}></p>
                        </div>
                        );
                    default:
                        return null;
                    }
                })}
                </div>
            );
        };

        const BookCardDisplay = ({ book, onSelectBook }) => { // Adapted from Vite's BookCard
            const { theme } = useContext(AppContext);
            const borderColorClass = theme === 'dark' ? `border-${book.themeColor}` : `border-${book.themeColor.replace('-500', '-300')}`;
            const hoverBorderColorClass = theme === 'dark' ? `hover:border-${book.themeColor.replace('-500', '-400')}` : `hover:border-${book.themeColor}`;
            const textColorClass = theme === 'dark' ? `text-${book.themeColor}` : `text-${book.themeColor.replace('-500','')}-700`; // e.g. text-pink-700

            return (
                <div className={`${theme === 'dark' ? 'bg-slate-800' : 'bg-white'} rounded-xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300 border-2 ${borderColorClass} ${hoverBorderColorClass}`}>
                <img 
                    className="w-full h-72 object-cover"
                    src={book.coverImage} 
                    alt={`Cover of ${book.title}`} 
                />
                <div className="p-6">
                    <h2 className={`text-2xl font-bold mb-2 ${textColorClass}`}>{book.title}</h2>
                    <p className={`${theme === 'dark' ? 'text-slate-400' : 'text-slate-500'} text-sm mb-1`}>By {book.author}</p>
                    <p className={`${theme === 'dark' ? 'text-slate-300' : 'text-slate-600'} text-base mb-6 h-20 overflow-y-auto`}>{book.description}</p>
                    <StyledButton 
                        onClick={() => onSelectBook(book.id)}
                        variant="primary" // Will use bookThemeColor if provided to StyledButton
                        bookThemeColor={book.themeColor}
                        size="lg"
                        className="w-full"
                    >
                        Explore Book
                    </StyledButton>
                </div>
                </div>
            );
        };
        
        const ChapterAccordionItem = ({ chapter, book, onSelectChapter }) => {
            const [isOpen, setIsOpen] = useState(false);
            const { userProgress, theme, speakAgentLeeText, voiceEnabled, markChapterAsRead: contextMarkChapterAsRead, addAward } = useContext(AppContext);
            
            const fullChapterId = `${book.id}-${chapter.id}`;
            const isRead = userProgress.readChapters.has(fullChapterId);
            const chapterAward = chapter.awardIdOnCompletion ? getAwardById(chapter.awardIdOnCompletion) : null;
            const hasEarnedChapterAward = chapterAward && userProgress.earnedAwards.has(chapterAward.id);

            const handleToggle = () => {
                const newIsOpenState = !isOpen;
                setIsOpen(newIsOpenState);
                if (newIsOpenState && !isRead) { 
                    contextMarkChapterAsRead(book.id, chapter.id); // This already handles award if defined in markChapterAsRead
                }
                if (newIsOpenState) {
                    speakAgentLeeText(`Opening ${chapter.title}.`);
                }
            };
            
            useEffect(() => { // Ensure award is given if chapter read from previous session
                if (isRead && chapter.awardIdOnCompletion && !userProgress.earnedAwards.has(chapter.awardIdOnCompletion)) {
                  addAward(chapter.awardIdOnCompletion);
                }
            }, [isRead, chapter.awardIdOnCompletion, userProgress.earnedAwards, addAward]);


            const headerBgColor = theme === 'dark' ? (isOpen ? 'bg-slate-700' : 'bg-slate-800') : (isOpen ? 'bg-gray-200' : 'bg-gray-100');
            const headerTextColor = theme === 'dark' ? `text-${book.themeColor}` : `text-${book.themeColor.replace('-500','')}-700`;
            const ringColor = theme === 'dark' ? `ring-${book.themeColor}` : `ring-${book.themeColor.replace('-500','')}-300`;
            
            return (
                <div className={`border ${theme === 'dark' ? 'border-slate-700' : 'border-gray-300'} rounded-lg mb-4 shadow-lg overflow-hidden transition-all duration-300 ${isOpen ? `ring-2 ${ringColor}` : ''}`}>
                <button
                    onClick={handleToggle}
                    className={`w-full ${headerBgColor} ${theme === 'dark' ? 'hover:bg-slate-700/80' : 'hover:bg-gray-200/80'} p-4 md:p-6 text-left flex justify-between items-center focus:outline-none`}
                >
                    <h3 className={`text-lg md:text-xl font-semibold ${headerTextColor}`}>
                    {isRead && !isOpen && <span title="Chapter Read" className="mr-2 text-green-500">✅</span>}
                    {chapter.title}
                    {hasEarnedChapterAward && <span title={`Awarded: ${chapterAward?.name}`} className={`ml-2 ${theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'}`}>{chapterAward?.icon}</span>}
                    </h3>
                    <span className={`transform transition-transform duration-300 text-2xl ${headerTextColor}`}>
                    {isOpen ? '−' : '+'}
                    </span>
                </button>
                {isOpen && (
                    <div className={`p-4 md:p-6 ${theme === 'dark' ? 'bg-slate-800/50' : 'bg-white'}`}>
                       <StyledButton onClick={() => onSelectChapter(book.id, chapter.id)} bookThemeColor={book.themeColor} variant="primary" className="mb-4">
                           Read Full Chapter
                       </StyledButton>
                       <p className={`${theme === 'dark' ? 'text-slate-400' : 'text-slate-600'} text-sm`}>
                           {chapter.content[1]?.text.substring(0,150) + "..."} (Click "Read Full Chapter" to view all)
                       </p>
                    </div>
                )}
                </div>
            );
        };

        const BadgeIconDisplay = ({ award, earned, size = 'md' }) => { // From Vite's BadgeIcon
            const { theme } = useContext(AppContext);
            const sizeClasses = {
                sm: 'w-16 h-16 text-3xl p-2',
                md: 'w-24 h-24 text-5xl p-3',
                lg: 'w-32 h-32 text-6xl p-4',
            };

            const opacityClass = earned ? 'opacity-100' : 'opacity-30 grayscale';
            
            let defaultBgColor = theme === 'dark' ? 'bg-slate-600' : 'bg-gray-200';
            let defaultBorderColor = theme === 'dark' ? 'border-slate-400' : 'border-gray-400';
            let nameColor = theme === 'dark' ? 'text-slate-100' : 'text-slate-700';
            let descColor = theme === 'dark' ? 'text-slate-300' : 'text-slate-500';

            if (earned) {
                if (award.bookId === 'mastery') {
                    defaultBgColor = theme === 'dark' ? 'bg-sky-700' : 'bg-sky-100';
                    defaultBorderColor = theme === 'dark' ? 'border-sky-400' : 'border-sky-300';
                    nameColor = theme === 'dark' ? 'text-sky-300' : 'text-sky-700';
                } else if (award.bookId === 'needleAndYarn') {
                    defaultBgColor = theme === 'dark' ? 'bg-pink-700' : 'bg-pink-100';
                    defaultBorderColor = theme === 'dark' ? 'border-pink-400' : 'border-pink-300';
                     nameColor = theme === 'dark' ? 'text-pink-300' : 'text-pink-700';
                } else { // General awards
                    defaultBgColor = theme === 'dark' ? 'bg-yellow-700' : 'bg-yellow-100';
                    defaultBorderColor = theme === 'dark' ? 'border-yellow-400' : 'border-yellow-300';
                    nameColor = theme === 'dark' ? 'text-yellow-300' : 'text-yellow-700';
                }
            } else { // Unearned
                defaultBgColor = theme === 'dark' ? 'bg-slate-800' : 'bg-gray-100';
                defaultBorderColor = theme === 'dark' ? 'border-slate-700' : 'border-gray-300';
                nameColor = theme === 'dark' ? 'text-slate-500' : 'text-gray-400';
                descColor = theme === 'dark' ? 'text-slate-600' : 'text-gray-500';
            }


            return (
                <div className="flex flex-col items-center text-center space-y-1 m-1">
                <div
                    className={`rounded-full flex items-center justify-center ${sizeClasses[size]} ${opacityClass} ${defaultBgColor} border-2 ${defaultBorderColor} shadow-lg transition-all duration-300 transform hover:scale-110`}
                    title={earned ? award.description : `${award.name} (Not earned yet)`}
                >
                    <span className="drop-shadow-md">{award.icon}</span>
                </div>
                <p className={`font-semibold text-xs sm:text-sm ${nameColor}`}>{award.name}</p>
                {size !== 'sm' && <p className={`text-xs ${descColor}`}>{award.description}</p>}
                </div>
            );
        };
        
        // --- Page/View Components ---
        const WelcomeModal = () => { // From original index.html
            const { showWelcome, setShowWelcome, speakAgentLeeText, theme } = useContext(AppContext);

            if (!showWelcome) return null;

            const handleClose = () => {
                setShowWelcome(false);
                const welcomeMsg = "Welcome to Crochet Adventures with Agent Leola! I'm Agent Leola, and I'll be your guide. Let's explore the wonderful world of crochet together! Tap the book icons to get started.";
                 speakAgentLeeText(welcomeMsg);
            }

            return (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className={`p-8 rounded-xl shadow-2xl max-w-lg w-full text-center ${theme === 'dark' ? 'dark-glassmorphism text-slate-100' : 'bg-[#faf3e0] text-slate-800'}`}>
                        <img src="https://picsum.photos/seed/agentleola/150/150" alt="Agent Leola" className="w-32 h-32 rounded-full mx-auto mb-6 border-4 border-[#d4a373] dark:border-[#38bdf8]" />
                        <h2 className={`text-3xl font-bold mb-4 ${theme === 'dark' ? 'text-[#38bdf8]' : 'text-[#8B4513]'}`}>Welcome, Future Crocheter!</h2>
                        <p className="mb-6 text-lg">I'm Agent Leola, your friendly guide on this exciting crochet journey. Together, we'll learn stitches, create beautiful things, and have a lot of fun!</p>
                        <StyledButton 
                            onClick={handleClose}
                            variant="primary" bookThemeColor={theme === 'dark' ? 'sky-500' : 'pink-500'} /* Example color */
                            size="lg"
                            className="px-8 py-3">
                            Let's Get Started!
                        </StyledButton>
                    </div>
                </div>
            );
        };

        const NarratorPanel = () => { // From original index.html
            const { narratorText, isNarrating, voiceEnabled, setVoiceEnabled, speakAgentLeeText, theme, setIsNarrating } = useContext(AppContext);

            const handlePlayPause = () => {
                if (isNarrating && voiceEnabled) {
                    speechSynthesis.pause(); 
                    setIsNarrating(false); 
                } else if (voiceEnabled) {
                    if (speechSynthesis.paused) {
                        speechSynthesis.resume();
                        setIsNarrating(true); 
                    } else {
                       speakAgentLeeText(narratorText);
                    }
                }
            };
            
            const toggleVoice = () => {
                const newVoiceEnabled = !voiceEnabled;
                setVoiceEnabled(newVoiceEnabled);
                if (!newVoiceEnabled && isNarrating) { 
                    speechSynthesis.cancel();
                    setIsNarrating(false);
                }
            }

            return (
                <div className={`fixed bottom-0 left-0 right-0 p-3 shadow-top z-30 ${theme === 'dark' ? 'dark-glassmorphism text-slate-200' : 'glassmorphism border-t border-gray-300 bg-opacity-80 bg-white'}`}>
                    <div className="container mx-auto flex items-center justify-between max-w-4xl">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <img src="https://picsum.photos/seed/agentleola/50/50" alt="Agent Leola" className={`w-12 h-12 rounded-full border-2 ${theme === 'dark' ? 'border-[#38bdf8]' : 'border-[#d4a373]'}`} />
                            <p className="text-sm md:text-base italic flex-grow min-w-0 break-words">{narratorText || "Agent Leola is here to help!"}</p>
                        </div>
                        <div className="flex space-x-2 flex-shrink-0">
                           {narratorText && (
                            <button onClick={handlePlayPause} className={`p-2 rounded-full  transition-colors ${theme === 'dark' ? 'hover:bg-slate-700' : 'hover:bg-gray-300'}`}>
                                {isNarrating && voiceEnabled ? <IconPause /> : <IconPlay />}
                            </button>
                           )}
                            <button onClick={toggleVoice}  className={`p-2 rounded-full transition-colors ${theme === 'dark' ? 'hover:bg-slate-700' : 'hover:bg-gray-300'}`}>
                                {voiceEnabled ? <IconVolumeUp /> : <IconVolumeOff />}
                            </button>
                        </div>
                    </div>
                </div>
            );
        };

        const HomePageView = () => {
            const { books, handleSelectBook, theme } = useContext(AppContext);
            const titleColor = theme === 'dark' ? 'text-sky-400' : 'text-[#8B4513]';
            const subTextColor = theme === 'dark' ? 'text-slate-300' : 'text-slate-600';
            return (
                <PageContainer>
                    <h1 className={`text-4xl lg:text-5xl font-bold ${titleColor} mb-6 text-center`}>Welcome to Crochet Learn & Play!</h1>
                    <p className={`text-lg lg:text-xl ${subTextColor} mb-10 lg:mb-12 text-center max-w-3xl mx-auto leading-relaxed`}>
                        Choose a book to start your crochet adventure. Learn new stitches with "Crochet Mastery" or enjoy a heartwarming story with "Needle & Yarn". Earn awards as you progress!
                    </p>
                    <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
                        {books.map(book => (
                        <BookCardDisplay key={book.id} book={book} onSelectBook={handleSelectBook} />
                        ))}
                    </div>
                </PageContainer>
            );
        };

        const BookView = () => {
            const { selectedBookId, books, setCurrentView, handleSelectChapter, theme } = useContext(AppContext);
            const book = books.find(b => b.id === selectedBookId);

            if (!book) {
                return <PageContainer title="Error"><p className="text-red-400 text-xl">Book not found!</p></PageContainer>;
            }
            
            const headerColorClass = theme === 'dark' ? `text-${book.themeColor}` : `text-${book.themeColor.replace('-500','')}-700`;
            const borderColorVar = theme === 'dark' ? `var(--tw-color-${book.themeColor})` : `var(--tw-color-${book.themeColor.replace('-500','')}-300)`;
            const bodyClass = document.body.classList;
            APP_BOOKS.forEach(b => { if(b.fontClass) bodyClass.remove(b.fontClass); }); // remove other font classes
            if(book.fontClass) bodyClass.add(book.fontClass); else if(theme !== 'dark') bodyClass.remove('font-guide-active');


            return (
                <PageContainer>
                    <button onClick={() => { setCurrentView('home'); document.body.classList.remove(book.fontClass); if(theme !== 'dark') document.body.classList.remove('font-guide-active');}} 
                        className={`mb-6 flex items-center px-4 py-2 rounded-lg transition-colors ${theme === 'dark' ? 'bg-slate-700 hover:bg-slate-600 text-slate-200' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`}>
                        <IconArrowLeft /> <span className="ml-2">Back to All Books</span>
                    </button>
                    <div className={`mb-10 p-4 md:p-6 ${theme === 'dark' ? 'bg-slate-800' : 'bg-white'} rounded-xl shadow-xl border-t-4 border-b-4`} style={{borderColor: borderColorVar}}>
                        <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                            <img 
                            src={book.coverImage} 
                            alt={`Cover of ${book.title}`} 
                            className="w-48 h-auto md:w-56 rounded-lg shadow-lg border-2 border-slate-700"
                            />
                            <div className="text-center md:text-left">
                            <h1 className={`text-3xl lg:text-4xl font-bold ${headerColorClass} mb-2`}>{book.title}</h1>
                            <p className={`text-md ${theme === 'dark' ? 'text-slate-400' : 'text-slate-500'} mb-3`}>By {book.author}</p>
                            <p className={`${theme === 'dark' ? 'text-slate-200' : 'text-slate-600'} text-md lg:text-lg leading-relaxed`}>{book.description}</p>
                            </div>
                        </div>
                    </div>
                    
                    <h2 className={`text-2xl lg:text-3xl font-semibold mb-6 ${headerColorClass}`}>Chapters</h2>
                    {book.chapters.length > 0 ? (
                        <div className="space-y-3">
                        {book.chapters.map(chapter => (
                            <ChapterAccordionItem key={chapter.id} chapter={chapter} book={book} onSelectChapter={handleSelectChapter} />
                        ))}
                        </div>
                    ) : (
                        <p className={`${theme === 'dark' ? 'text-slate-400' : 'text-slate-600'} text-lg`}>No chapters available for this book yet.</p>
                    )}
                </PageContainer>
            );
        };
        
        const ChapterView = () => {
            const { selectedBookId, selectedChapterId, books, setCurrentView, handleStartQuiz, theme } = useContext(AppContext);
            const book = books.find(b => b.id === selectedBookId);
            const chapter = book?.chapters.find(c => c.id === selectedChapterId);

            if (!book || !chapter) {
                return <PageContainer title="Error"><p className="text-red-400 text-xl">Chapter not found!</p></PageContainer>;
            }
            
            const headerColorClass = theme === 'dark' ? `text-${book.themeColor}` : `text-${book.themeColor.replace('-500','')}-700`;
            const bodyClass = document.body.classList;
            APP_BOOKS.forEach(b => { if(b.fontClass) bodyClass.remove(b.fontClass); });
            if(book.fontClass) bodyClass.add(book.fontClass); else if(theme !== 'dark') bodyClass.remove('font-guide-active');


            return (
                <PageContainer>
                     <button onClick={() => setCurrentView('bookView')} 
                        className={`mb-6 flex items-center px-4 py-2 rounded-lg transition-colors ${theme === 'dark' ? 'bg-slate-700 hover:bg-slate-600 text-slate-200' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`}>
                        <IconArrowLeft /> <span className="ml-2">Back to Chapters</span>
                    </button>
                    <article className={`p-4 md:p-6 rounded-lg shadow-xl ${theme === 'dark' ? 'dark-glassmorphism' : 'bg-white bg-opacity-90'}`}>
                        <h1 className={`text-3xl font-bold mb-6 ${headerColorClass}`}>{chapter.title}</h1>
                        <ContentRenderer content={chapter.content} bookThemeColor={book.themeColor} />
                        
                        {chapter.quizId && (
                        <div className="mt-8 pt-6 border-t ${theme === 'dark' ? 'border-slate-700' : 'border-gray-300'} text-center">
                            <StyledButton 
                                onClick={() => handleStartQuiz(chapter.quizId)} 
                                variant="primary"
                                bookThemeColor={book.themeColor}
                                size="lg">
                            Take Quiz: {getAwardById(getQuizById(chapter.quizId)?.awardIdOnCompletion)?.name || 'Chapter Challenge'} 🧠
                            </StyledButton>
                        </div>
                        )}
                    </article>
                </PageContainer>
            );
        };

        const QuizView = () => {
            const { selectedQuizId, quizzes, processQuizCompletion, setCurrentView, theme, userProgress } = useContext(AppContext);
            const quiz = quizzes.find(q => q.id === selectedQuizId);

            const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
            const [selectedAnswers, setSelectedAnswers] = useState({});
            const [showResults, setShowResults] = useState(false);

            useEffect(() => { // Reset state if quiz changes
                setCurrentQuestionIndex(0);
                setSelectedAnswers({});
                setShowResults(false);
            }, [selectedQuizId]);
            
            if (!quiz) return <PageContainer title="Error"><p className="text-red-400 text-xl">Quiz not found.</p></PageContainer>;
            
            const book = getBookById(quiz.bookId);
            const currentQuestion = quiz.questions[currentQuestionIndex];

            const handleAnswerSelect = (questionId, optionId) => {
                setSelectedAnswers(prev => ({ ...prev, [questionId]: optionId }));
            };

            const handleNextQuestion = () => {
                if (currentQuestionIndex < quiz.questions.length - 1) {
                setCurrentQuestionIndex(prev => prev + 1);
                } else {
                // All questions answered, show results
                const score = calculateScore();
                processQuizCompletion(quiz.id, score, quiz.questions.length);
                setShowResults(true);
                }
            };

            const calculateScore = () => {
                let score = 0;
                quiz.questions.forEach(q => {
                if (selectedAnswers[q.id] === q.correctOptionId) {
                    score++;
                }
                });
                return score;
            };

            if (showResults) {
                const score = calculateScore();
                const totalQuestions = quiz.questions.length;
                const percentage = Math.round((score / totalQuestions) * 100);
                const awardForQuiz = getAwardById(quiz.awardIdOnCompletion);
                const quizPassed = score >= totalQuestions / 2;

                return (
                <PageContainer title={`${quiz.title} - Results`}>
                    <div className={`${theme === 'dark' ? 'bg-slate-800' : 'bg-white'} p-6 sm:p-8 rounded-lg shadow-xl text-center`}>
                    <p className={`text-2xl ${theme === 'dark' ? 'text-slate-200' : 'text-slate-700'} mb-2`}>You scored: <strong className={`text-${book?.themeColor || 'pink-500'}`}>{score}</strong> out of <strong className={`text-${book?.themeColor || 'pink-500'}`}>{totalQuestions}</strong> ({percentage}%)</p>
                    
                    {awardForQuiz && userProgress.earnedAwards.has(awardForQuiz.id) && quizPassed && (
                        <div className="my-6">
                        <p className={`text-lg ${theme === 'dark' ? 'text-slate-300' : 'text-slate-600'} mb-2`}>You've earned an award:</p>
                        <div className={`inline-block ${theme === 'dark' ? 'bg-slate-700' : 'bg-gray-100'} p-4 rounded-lg shadow-md`}>
                            <span className="text-5xl mr-2">{awardForQuiz.icon}</span>
                            <span className={`text-2xl font-semibold ${theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'}`}>{awardForQuiz.name}</span>
                            <p className={`text-sm ${theme === 'dark' ? 'text-slate-400' : 'text-gray-500'} mt-1`}>{awardForQuiz.description}</p>
                        </div>
                        </div>
                    )}
                    
                    <p className={`text-lg ${theme === 'dark' ? 'text-slate-300' : 'text-slate-600'} mt-6 mb-4`}>
                        {quizPassed ? "Great job! You're a crochet star! ✨" : "Good effort! Keep practicing! 👍"}
                    </p>
                    <StyledButton onClick={() => setCurrentView('chapterView')} variant="primary" bookThemeColor={book?.themeColor} size="lg">Back to Chapter</StyledButton>
                    <StyledButton onClick={() => setCurrentView('awardsView')} variant="secondary" bookThemeColor={book?.themeColor === 'sky-500' ? 'pink-500' : 'sky-500'} size="lg" className="ml-4">View My Awards</StyledButton>
                    </div>
                </PageContainer>
                );
            }

            if (!currentQuestion) {
                return <PageContainer title="Error"><p className="text-red-400 text-xl">Quiz question not found.</p></PageContainer>;
            }

            return (
                <PageContainer title={quiz.title}>
                <div className={`${theme === 'dark' ? 'bg-slate-800' : 'bg-white'} p-6 sm:p-8 rounded-lg shadow-xl w-full max-w-2xl mx-auto`}>
                    <p className={`text-sm ${theme === 'dark' ? 'text-slate-400' : 'text-slate-500'} mb-2`}>Question {currentQuestionIndex + 1} of {quiz.questions.length}</p>
                    <h3 className={`text-2xl sm:text-3xl font-semibold ${theme === 'dark' ? `text-${book?.themeColor.split('-')[0] + '-300'}` : `text-${book?.themeColor.split('-')[0] + '-700'}`} mb-6 leading-tight`}>{currentQuestion.text}</h3>
                    <div className="space-y-4 mb-8">
                    {currentQuestion.options.map(option => (
                        <button
                        key={option.id}
                        onClick={() => handleAnswerSelect(currentQuestion.id, option.id)}
                        className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 text-lg focus:outline-none 
                            ${selectedAnswers[currentQuestion.id] === option.id 
                            ? `${theme === 'dark' ? `bg-${book?.themeColor} border-${book?.themeColor.split('-')[0] + '-400'}` : `bg-${book?.themeColor.split('-')[0] + '-600'} border-${book?.themeColor.split('-')[0] + '-700'}`} text-white ring-2 ${theme === 'dark' ? `ring-${book?.themeColor.split('-')[0] + '-300'}` : `ring-${book?.themeColor.split('-')[0] + '-400'}`} ring-offset-2 ${theme === 'dark' ? 'ring-offset-slate-800' : 'ring-offset-white'}`
                            : `${theme === 'dark' ? `bg-slate-700 border-slate-600 hover:bg-slate-600/70 hover:border-${book?.themeColor.split('-')[0] + '-500'} text-slate-200` : `bg-gray-100 border-gray-300 hover:bg-gray-200 hover:border-${book?.themeColor.split('-')[0] + '-500'} text-slate-700`}`
                            }`}
                        >
                        {option.text}
                        </button>
                    ))}
                    </div>
                    <StyledButton 
                    onClick={handleNextQuestion} 
                    disabled={!selectedAnswers[currentQuestion.id]}
                    size="lg"
                    className="w-full"
                    variant="primary"
                    bookThemeColor={book?.themeColor}
                    >
                    {currentQuestionIndex < quiz.questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
                    </StyledButton>
                </div>
                </PageContainer>
            );
        };

        const AwardsView = () => {
            const { awards, userProgress, resetAllProgress, theme } = useContext(AppContext);

            const earnedAwardsList = awards.filter(award => userProgress.earnedAwards.has(award.id));
            const unearnedAwardsList = awards.filter(award => !userProgress.earnedAwards.has(award.id));
            
            const titleColor = theme === 'dark' ? 'text-sky-400' : 'text-[#8B4513]';
            const earnedHeaderColor = theme === 'dark' ? 'text-green-400' : 'text-green-600';
            const unearnedHeaderColor = theme === 'dark' ? 'text-slate-500' : 'text-slate-500';

            return (
                <PageContainer title="My Awards Collection 🏆">
                {earnedAwardsList.length === 0 && unearnedAwardsList.length === awards.length && (
                    <p className={`text-xl ${theme === 'dark' ? 'text-slate-300' : 'text-slate-600'} text-center mb-8`}>
                    You haven't earned any awards yet. Keep learning and completing quizzes to collect them all!
                    </p>
                )}

                {earnedAwardsList.length > 0 && (
                    <>
                    <h2 className={`text-3xl font-semibold ${earnedHeaderColor} mb-6`}>Earned Awards! ({earnedAwardsList.length})</h2>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4 mb-12">
                        {earnedAwardsList.map(award => (
                        <BadgeIconDisplay key={award.id} award={award} earned={true} size="md" />
                        ))}
                    </div>
                    </>
                )}
                
                {unearnedAwardsList.length > 0 && earnedAwardsList.length > 0 && (
                    <hr className={`my-10 ${theme === 'dark' ? 'border-slate-700' : 'border-gray-300'}`} />
                )}

                {unearnedAwardsList.length > 0 && (
                    <>
                    <h2 className={`text-3xl font-semibold ${unearnedHeaderColor} mb-6`}>Awards to Unlock ({unearnedAwardsList.length})</h2>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4">
                        {unearnedAwardsList.map(award => (
                        <BadgeIconDisplay key={award.id} award={award} earned={false} size="md" />
                        ))}
                    </div>
                    </>
                )}
                
                <div className="mt-16 text-center">
                    <StyledButton onClick={resetAllProgress} variant="danger" size="sm">
                    Reset All Progress
                    </StyledButton>
                </div>
                </PageContainer>
            );
        };
        
        // --- Main App Component ---
        const App = () => {
            const { currentView, setCurrentView, theme, toggleTheme, selectedBookId } = useContext(AppContext);

            const NavButton = ({ view, icon, label }) => {
                const book = getBookById(selectedBookId);
                let activeColorClass;
                if (theme === 'dark') {
                    activeColorClass = book && currentView === view ? `bg-${book.themeColor.split('-')[0] + '-600'}` : 'bg-sky-600';
                } else {
                    activeColorClass = book && currentView === view ? `bg-${book.themeColor.replace('-500','')}-600` : 'bg-[#8B4513]';
                }

                return (
                    <button onClick={() => setCurrentView(view)} 
                            title={label}
                            className={`p-2 md:p-3 rounded-full transition-colors duration-300 
                                        ${currentView === view 
                                            ? `${activeColorClass} text-white` 
                                            : (theme === 'dark' ? 'text-slate-300 hover:bg-slate-700 hover:text-sky-400' : 'text-gray-600 hover:bg-gray-200 hover:text-[#8B4513]')}`}>
                    {icon} <span className="hidden sm:inline ml-1">{label}</span>
                    </button>
                );
            }
            
            return (
                <div className={`min-h-screen transition-colors duration-300 ${theme === 'dark' ? 'bg-slate-900 text-slate-100' : 'bg-[#fdf6e3] text-slate-800'}`}>
                    <WelcomeModal />
                    <header className={`sticky top-0 z-40 p-3 shadow-md ${theme === 'dark' ? 'dark-glassmorphism' : 'glassmorphism bg-opacity-90 bg-white'}`}>
                        <div className="container mx-auto flex justify-between items-center max-w-6xl">
                            <div className="flex items-center cursor-pointer" onClick={() => setCurrentView('home')}>
                                <img src="https://picsum.photos/seed/appicon/40/40" alt="App Icon" className="w-8 h-8 md:w-10 md:h-10 rounded-md mr-2 md:mr-3" />
                                <h1 className={`text-lg md:text-2xl font-bold ${theme === 'dark' ? 'text-sky-400' : 'text-[#8B4513]'}`}>Crochet Learn & Play</h1>
                            </div>
                            <nav className="flex items-center space-x-1 md:space-x-2">
                                <NavButton view="home" icon={<IconBookOpen />} label="Books" />
                                <NavButton view="awardsView" icon={<IconTrophy />} label="Awards" />
                                <button onClick={toggleTheme} title="Toggle Theme" className={`p-2 md:p-3 rounded-full transition-colors duration-300 ${theme === 'dark' ? 'text-slate-300 hover:bg-slate-700 hover:text-yellow-400' : 'text-gray-600 hover:bg-gray-200 hover:text-yellow-500'}`}>
                                    {theme === 'light' ? <IconMoon /> : <IconLightBulb />}
                                </button>
                            </nav>
                        </div>
                    </header>
                    
                    <main className="container mx-auto max-w-6xl pb-24"> {/* pb for narrator panel */}
                        {currentView === 'home' && <HomePageView />}
                        {currentView === 'bookView' && <BookView />}
                        {currentView === 'chapterView' && <ChapterView />}
                        {currentView === 'quizView' && <QuizView />}
                        {currentView === 'awardsView' && <AwardsView />}
                    </main>

                    <NarratorPanel />
                     <footer className={`text-center p-4 text-sm border-t ${theme === 'dark' ? 'bg-slate-800 text-slate-400 border-slate-700' : 'bg-gray-100 text-gray-500 border-gray-300'}`}>
                        © ${new Date().getFullYear()} Crochet Learn & Play by Sista Lee. Happy Hooking!
                    </footer>
                </div>
            );
        };
        
        // --- Service Worker Registration --- (Copied from original prompt's index.html)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js') 
                    .then(registration => console.log('ServiceWorker registration successful with scope: ', registration.scope))
                    .catch(error => console.log('ServiceWorker registration failed: ', error));
            });
        }
        
        const RootComponent = () => (
            <AppProvider>
                <App />
            </AppProvider>
        );

        ReactDOM.createRoot(document.getElementById('root')).render(<RootComponent />);
    </script>
    
</body>
</html>