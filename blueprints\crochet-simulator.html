<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Crochet Simulator</title>
    <link rel="stylesheet" href="../css/agent-lee.css">
    <script src="../js/badge-manager.js"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #DAA06D;
            --yarn-color: #FF7F50;
            --bg-color: #FAF3E0;
            --card-bg: #FFF8E8;
            --text-color: #333;
            --shadow-color: rgba(139, 69, 19, 0.2);
            --footer-bg: #704214;
            --footer-text: #F4ECD8;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--bg-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: rgba(255, 248, 232, 0.92);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.25);
            padding: 15px;
            text-align: center;
        }
        
        .title {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: var(--secondary-color);
            font-size: 1.2rem;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .section-title {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .hidden {
            display: none;
        }
        
        /* Intro Section */
        #intro-section {
            text-align: center;
        }
        
        .intro-text {
            margin-bottom: 20px;
        }
        
        .difficulty-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .difficulty-btn {
            padding: 10px 20px;
            background-color: var(--card-bg);
            border: 2px solid var(--secondary-color);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .difficulty-btn:hover, .difficulty-btn.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .start-btn {
            padding: 12px 30px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .start-btn:hover {
            background-color: var(--yarn-color);
            transform: translateY(-3px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        /* Simulator Section */
        #simulator-section {
            display: flex;
            flex-direction: column;
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            padding: 8px 15px;
            background-color: var(--card-bg);
            border: 1px solid var(--secondary-color);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .view-btn:hover, .view-btn.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .game-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .game-btn:hover {
            background-color: var(--yarn-color);
        }
        
        .game-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            background-color: rgba(255,255,255,0.7);
            padding: 10px;
            border-radius: 5px;
        }
        
        .stat {
            text-align: center;
            padding: 5px 10px;
        }
        
        .stat-label {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .instructions {
            background-color: rgba(255,255,255,0.7);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .step-counter {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        #step-instruction {
            text-align: center;
        }
        
        .game-status {
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            background-color: rgba(255,255,255,0.7);
        }
        
        /* Crochet Board */
        .crochet-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        #crochet-board {
            width: 100%;
            height: 400px;
            background-color: white;
            border-radius: 5px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .crochet-grid {
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            grid-template-rows: repeat(15, 1fr);
            width: 100%;
            height: 100%;
        }
        
        .crochet-cell {
            border: 1px solid #eee;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .crochet-cell:hover {
            background-color: rgba(255, 127, 80, 0.1);
        }
        
        .crochet-cell.stitch {
            background-color: var(--secondary-color);
            border-radius: 50%;
        }
        
        .crochet-tools {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .tool {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .tool:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .tool.active {
            border: 3px solid var(--yarn-color);
        }
        
        .tool-icon {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .tool-name {
            font-size: 0.8rem;
        }
        
        .yarn-colors {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .yarn-color {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #eee;
        }
        
        .yarn-color:hover, .yarn-color.active {
            transform: scale(1.2);
            border-color: var(--primary-color);
        }
        
        /* Pattern Steps */
        #pattern-steps {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .pattern-step {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: white;
            border-left: 3px solid var(--secondary-color);
            transition: all 0.3s ease;
        }
        
        .pattern-step:hover {
            transform: translateX(5px);
        }
        
        .pattern-step.active {
            background-color: var(--secondary-color);
            color: white;
            border-left-color: var(--primary-color);
        }
        
        .pattern-step.completed {
            background-color: #e8f5e9;
            border-left-color: #4caf50;
            text-decoration: line-through;
            opacity: 0.7;
        }
        
        /* Help Overlay */
        #help-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        #help-overlay.visible {
            display: flex;
        }
        
        .help-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        .help-title {
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .help-section {
            margin-bottom: 20px;
        }
        
        .help-section h3 {
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        
        /* Result Modal */
        #result-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: relative;
        }
        
        .close {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        #result-title {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        #result-message {
            margin-bottom: 20px;
        }
        
        #result-score {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        #play-again {
            background-color: var(--primary-color);
            color: white;
        }
        
        #try-harder {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .modal-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 10px;
            }
            
            .view-controls {
                justify-content: center;
            }
            
            .game-stats {
                flex-wrap: wrap;
            }
            
            .stat {
                flex: 1;
                min-width: 33%;
            }
            
            .crochet-tools {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .tool {
                width: 60px;
                height: 60px;
            }
            
            .yarn-colors {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1 class="title">Interactive Crochet Simulator</h1>
        <p class="subtitle">Learn crochet through this interactive 2D/3D simulation</p>
    </header>
    
    <main class="main-container">
        <!-- Introduction Section -->
        <section id="intro-section" class="section">
            <h2 class="section-title">Welcome to the Crochet Simulator!</h2>
            <div class="intro-text">
                <p>This interactive 2D/3D simulator will help you learn crochet patterns and techniques in a fun, engaging way. Follow the instructions, create beautiful virtual projects, and earn badges!</p>
                <p>Choose your difficulty level to begin:</p>
            </div>
            
            <div class="difficulty-selector">
                <button class="difficulty-btn active" data-difficulty="easy">Beginner</button>
                <button class="difficulty-btn" data-difficulty="medium">Intermediate</button>
                <button class="difficulty-btn" data-difficulty="hard">Advanced</button>
            </div>
            
            <button id="start-simulator" class="start-btn">Start Simulator</button>
        </section>
        
        <!-- Simulator Section -->
        <section id="simulator-section" class="section hidden">
            <div class="controls">
                <div class="view-controls">
                    <button class="view-btn active" data-view="normal">Normal View</button>
                    <button class="view-btn" data-view="3d">3D View</button>
                    <button class="view-btn" data-view="tutorial">Tutorial View</button>
                </div>
                
                <div class="game-controls">
                    <button id="help-btn" class="game-btn">Help</button>
                    <button id="reset-btn" class="game-btn">Reset</button>
                    <button id="back-to-menu" class="game-btn">Back to Menu</button>
                </div>
            </div>
            
            <div class="game-stats">
                <div class="stat">
                    <div class="stat-label">Score</div>
                    <div id="score">0</div>
                </div>
                
                <div class="stat">
                    <div class="stat-label">Progress</div>
                    <div id="progress">0%</div>
                </div>
                
                <div class="stat">
                    <div class="stat-label">Time</div>
                    <div id="time">00:00</div>
                </div>
            </div>
            
            <div class="game-status" id="game-status">
                <p id="status-message">Get ready to start crocheting!</p>
            </div>
            
            <div class="instructions">
                <div class="step-counter">
                    Step <span id="current-step-number">1</span> of <span id="total-steps">10</span>
                </div>
                <p id="step-instruction">Select a yarn color to begin your project.</p>
            </div>
            
            <div class="crochet-container">
                <div id="crochet-board" class="crochet-board">
                    <!-- Crochet grid will be generated here by JavaScript -->
                </div>
                
                <div class="crochet-tools">
                    <div class="tool" data-tool="hook">
                        <div class="tool-icon">🪝</div>
                        <div class="tool-name">Hook</div>
                    </div>
                    
                    <div class="tool" data-tool="yarn">
                        <div class="tool-icon">🧶</div>
                        <div class="tool-name">Yarn</div>
                    </div>
                    
                    <div class="tool" data-tool="scissors">
                        <div class="tool-icon">✂️</div>
                        <div class="tool-name">Scissors</div>
                    </div>
                    
                    <div class="tool" data-tool="undo">
                        <div class="tool-icon">↩️</div>
                        <div class="tool-name">Undo</div>
                    </div>
                </div>
                
                <div class="yarn-colors">
                    <div class="yarn-color" style="background-color: #FF7F50;" data-color="#FF7F50"></div>
                    <div class="yarn-color" style="background-color: #4682B4;" data-color="#4682B4"></div>
                    <div class="yarn-color" style="background-color: #9ACD32;" data-color="#9ACD32"></div>
                    <div class="yarn-color" style="background-color: #BA55D3;" data-color="#BA55D3"></div>
                    <div class="yarn-color" style="background-color: #FFD700;" data-color="#FFD700"></div>
                </div>
            </div>
            
            <div id="pattern-steps">
                <!-- Pattern steps will be generated here by JavaScript -->
            </div>
        </section>
    </main>
    
    <!-- Agent Lee's Help Box -->
    <div id="help-overlay">
        <div class="help-content">
            <button id="close-help" class="close-btn" onclick="document.getElementById('help-overlay').classList.remove('visible');">×</button>
            <h2 class="help-title">Sister Lee's Crochet Tips</h2>
            
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <div style="flex: 0 0 80px; margin-right: 15px;">
                    <img src="../images/sister-lee/g444d1542de54243d3e02fe079aab4205c00a05b232cc40066cb39f802281f98c3b9bda3530c79a1a1dc6f1f9b036b879_640.png" alt="Sister Lee" style="width: 80px; height: 80px; border-radius: 50%;">
                </div>
                <div>
                    <p style="font-style: italic; color: #8B4513;">"Welcome to my crochet simulator! I'm here to help you learn the joy of crocheting in a fun, interactive way. Just pick your yarn, grab your hook, and follow along with the pattern."</p>
                </div>
            </div>
            
            <p>Select your yarn color first, then activate your hook. The game will highlight where to place your stitches. Just click on the highlighted areas to create your crochet project!</p>
            
            <p>Don't worry about making mistakes - this is how we learn! Try different yarn colors and see how they change your creation.</p>
            
            <button class="start-btn" style="margin-top: 20px;" onclick="document.getElementById('help-overlay').classList.remove('visible');">Start Crocheting!</button>
        </div>
    </div>
    
    <!-- Result Modal -->
    <div id="result-modal" class="hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="result-title">Congratulations!</h2>
            <p id="result-message">You've completed the pattern!</p>
            <div id="result-score">Score: 0</div>
            <div class="modal-buttons">
                <button id="play-again" class="modal-btn">Play Again</button>
                <button id="try-harder" class="modal-btn">Try Harder Level</button>
            </div>
        </div>
    </div>
    
    <script src="../js/crochet-simulator.js"></script>
</body>
</html>