:root {
    --primary-color: #34648C;
    --secondary-color: #8FB9D8;
    --accent-color: #F89C74;
    --success-color: #4CAF50;
    --error-color: #F44336;
    --bg-color: #F5F9FC;
    --card-bg: #FFFFFF;
    --text-color: #333;
    --shadow-color: rgba(52, 100, 140, 0.2);
    --yarn-pink: #ff8ab4;
    --yarn-blue: #7eb6ff;
    --yarn-green: #a0e57c;
    --yarn-purple: #c17bff;
    --yarn-yellow: #ffd95c;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px 0;
    text-align: center;
    box-shadow: 0 2px 10px var(--shadow-color);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    line-height: 1.2;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
    line-height: 1.4;
}

main {
    flex: 1;
    padding: 30px 0;
}

.game-container {
    background-color: var(--card-bg);
    border-radius: 20px;
    box-shadow: 0 8px 16px var(--shadow-color);
    padding: 30px;
    margin-bottom: 30px;
    transition: transform 0.3s ease;
}

.game-container:hover {
    transform: translateY(-5px);
}

h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.8rem;
    text-align: center;
    line-height: 1.3;
}

.btn {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 30px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: bold;
    margin: 10px 5px;
    min-height: 44px; /* Touch-friendly minimum */
}

.btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px var(--shadow-color);
}

.btn:active {
    transform: translateY(0);
}

.btn.primary {
    background-color: var(--primary-color);
}

.btn.accent {
    background-color: var(--accent-color);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(248, 156, 116, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(248, 156, 116, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(248, 156, 116, 0);
    }
}

.img-responsive {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto 20px;
    border-radius: 10px;
}

.instructions {
    background-color: rgba(143, 185, 216, 0.1);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border-left: 5px solid var(--primary-color);
}

.instructions h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    line-height: 1.3;
}

.instruction-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.step-number {
    background-color: var(--accent-color);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-weight: bold;
    flex-shrink: 0;
    font-size: 1rem;
}

.step-text {
    flex-grow: 1;
    line-height: 1.5;
    font-size: 1rem;
}

.current-step-box {
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.current-step-box h3 {
    margin-bottom: 10px;
    font-size: 1.2rem;
    line-height: 1.3;
}

.current-step-box p {
    font-size: 1.1rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.step-progress {
    font-size: 1rem;
    opacity: 0.9;
}

.difficulty-selector {
    text-align: center;
    margin-bottom: 30px;
}

.difficulty-selector h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.difficulty-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 3px solid var(--secondary-color);
    border-radius: 15px;
    padding: 15px 20px;
    margin: 0 10px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    min-height: 80px;
}

.difficulty-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px var(--shadow-color);
}

.difficulty-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-icon {
    font-size: 2rem;
    margin-bottom: 5px;
}

.btn-text {
    text-align: center;
    font-weight: bold;
    line-height: 1.3;
}

.btn-text small {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: normal;
    display: block;
    margin-top: 2px;
}

.stitches-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stitch-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 10px var(--shadow-color);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
    width: 100%;
}

.stitch-card:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px var(--shadow-color);
}

.stitch-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.stitch-card h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
    line-height: 1.3;
}

.stitch-card p {
    font-size: 0.95rem;
    line-height: 1.4;
    color: var(--text-color);
}

.hidden {
    display: none;
}/* Crochet simulator specific styles */
.crochet-container {
    position: relative;
    width: 100%;
    height: 600px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 4px 10px var(--shadow-color);
    margin-bottom: 20px;
    touch-action: manipulation;
    perspective: 1000px;
}

.crochet-board {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.5s ease;
}

.yarn-container {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 6px 15px var(--shadow-color);
    z-index: 10;
}

.yarn-ball {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    background-size: cover;
    background-position: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    min-width: 70px; /* Prevent shrinking */
}

.yarn-ball::before {
    content: '🧶';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    opacity: 0.8;
}

.yarn-ball:hover {
    transform: scale(1.15);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.yarn-ball.selected {
    transform: scale(1.2);
    box-shadow: 0 0 0 4px white, 0 0 0 7px var(--accent-color), 0 8px 16px rgba(0, 0, 0, 0.3);
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: scale(1.2) translateY(0);
    }
    40% {
        transform: scale(1.2) translateY(-10px);
    }
    80% {
        transform: scale(1.2) translateY(-5px);
    }
}

.pink {
    background-color: var(--yarn-pink);
}

.blue {
    background-color: var(--yarn-blue);
}

.green {
    background-color: var(--yarn-green);
}

.purple {
    background-color: var(--yarn-purple);
}

.yellow {
    background-color: var(--yarn-yellow);
}

.hook-container {
    position: absolute;
    bottom: 30px;
    right: 30px;
    z-index: 10;
}

.hook {
    width: 100px;
    height: 160px;
    background: linear-gradient(145deg, #a0a0a0, #d0d0d0);
    border-radius: 5px 5px 50% 50% / 5px 5px 20px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.hook::before {
    content: '🪝';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
}

.hook:hover {
    transform: scale(1.1) rotate(-5deg);
}

.hook.active {
    transform: translateY(-15px) rotate(10deg);
    box-shadow: 0 8px 20px rgba(248, 156, 116, 0.5);
    background: linear-gradient(145deg, var(--accent-color), #ff7043);
    animation: hookActive 1s ease infinite;
}

@keyframes hookActive {
    0%, 100% {
        transform: translateY(-15px) rotate(10deg);
    }
    50% {
        transform: translateY(-20px) rotate(15deg);
    }
}

/* New Pattern Instructions Container (Below the Game Board) */
.pattern-instructions-container {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 10px var(--shadow-color);
    margin: 20px 0;
    border-left: 5px solid var(--accent-color);
}

.pattern-title {
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.2rem;
    line-height: 1.3;
}

.pattern-steps {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.pattern-steps li {
    padding: 8px 15px;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    line-height: 1.4;
    background-color: rgba(143, 185, 216, 0.1);
    border-radius: 8px;
    flex: 1;
    min-width: 200px;
}

.pattern-steps li.completed {
    color: var(--success-color);
    text-decoration: line-through;
    opacity: 0.7;
    background-color: rgba(76, 175, 80, 0.1);
}

.pattern-steps li.current {
    font-weight: bold;
    color: var(--accent-color);
    background-color: rgba(248, 156, 116, 0.1);
    border-left: 3px solid var(--accent-color);
}

.pattern-steps li:before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 10px;
    border-radius: 50%;
    background-color: #ddd;
    flex-shrink: 0;
}

.pattern-steps li.completed:before {
    background-color: var(--success-color);
    content: '✓';
    color: white;
    font-size: 8px;
    text-align: center;
    line-height: 12px;
}

.pattern-steps li.current:before {
    background-color: var(--accent-color);
    animation: pulse 1.5s infinite;
}.stitch-grid {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(6, 50px);
    grid-template-rows: repeat(6, 50px);
    gap: 8px;
}

.stitch-cell {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
    min-width: 50px; /* Prevent shrinking */
    min-height: 50px;
}

.stitch-cell:hover {
    background-color: rgba(143, 185, 216, 0.8);
    transform: scale(1.05);
}

.stitch-cell.highlight {
    background-color: rgba(248, 156, 116, 0.9);
    border-color: var(--accent-color);
    animation: glow 1.5s infinite;
    box-shadow: 0 0 15px rgba(248, 156, 116, 0.6);
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(248, 156, 116, 0.6);
    }
    50% {
        box-shadow: 0 0 25px rgba(248, 156, 116, 0.9);
    }
}

.stitch-cell.completed {
    background-color: rgba(76, 175, 80, 0.8);
    border-color: var(--success-color);
}

.stitch-cell.wrong {
    background-color: rgba(244, 67, 54, 0.8);
    border-color: var(--error-color);
    animation: shake 0.5s ease;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    20%, 60% {
        transform: translateX(-8px);
    }
    40%, 80% {
        transform: translateX(8px);
    }
}

.stitch {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 8px;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.5s ease;
    transform-style: preserve-3d;
}

.stitch.visible {
    opacity: 1;
    animation: stitchAppear 0.5s ease;
}

@keyframes stitchAppear {
    0% {
        opacity: 0;
        transform: scale(0.5) rotate(180deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

.stitch-chain {
    background-color: currentColor;
    width: 70%;
    height: 70%;
    margin: 15%;
    border-radius: 50%;
}

.stitch-single {
    background-color: currentColor;
    width: 80%;
    height: 80%;
    margin: 10%;
    border-radius: 8px;
}

.stitch-double {
    background-color: currentColor;
    width: 90%;
    height: 90%;
    margin: 5%;
    border-radius: 5px;
}

.game-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px 30px;
    border-radius: 15px;
    font-size: 1.2rem;
    text-align: center;
    z-index: 20;
    max-width: 80%;
}

.score-container {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    flex-wrap: wrap;
    gap: 10px;
}

.score-box {
    background-color: var(--card-bg);
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 8px var(--shadow-color);
    text-align: center;
    flex: 1;
    min-width: 120px;
    margin: 5px;
}

.score-box h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1rem;
    line-height: 1.2;
}

.score-box p {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
    line-height: 1.2;
}

.view-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.view-btn {
    padding: 10px 20px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-height: 44px;
}

.view-btn:hover {
    background-color: var(--primary-color);
}

.view-btn.active {
    background-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(52, 100, 140, 0.5);
}

.controls-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.help-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    padding: 20px;
    text-align: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.help-overlay.visible {
    opacity: 1;
    pointer-events: auto;
}

.help-content {
    max-width: 500px;
    width: 100%;
}

.help-content h3 {
    margin-bottom: 30px;
    color: var(--accent-color);
    font-size: 1.5rem;
    line-height: 1.3;
}

.help-step {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    text-align: left;
}

.help-number {
    font-size: 1.5rem;
    margin-right: 15px;
    flex-shrink: 0;
}

.help-step p {
    line-height: 1.5;
}

.close-help {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    min-width: 44px;
}

.close-help:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 100;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 20px;
    padding: 40px;
    max-width: 500px;
    width: 100%;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.modal h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    line-height: 1.3;
}

.modal p {
    margin-bottom: 20px;
    line-height: 1.6;
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}

footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: auto;
}

footer p {
    opacity: 0.9;
    line-height: 1.4;
}

/* Confetti canvas styles */
#confetti-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    pointer-events: none;
}

/* Donation Banner Styles */
.donation-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 40px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.donation-message {
    flex: 2;
    padding: 10px;
}

.donation-message h3 {
    margin-bottom: 10px;
    font-size: 1.4rem;
}

.donation-message p {
    font-size: 1rem;
    opacity: 0.9;
}

.donation-banner .btn {
    flex: 1;
    max-width: 200px;
    margin: 10px;
    background-color: white;
    color: var(--primary-color);
}

.donation-banner .btn:hover {
    background-color: var(--accent-color);
    color: white;
}

.footer-links {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: white;
    text-decoration: underline;
}

/* Tablet Responsive Styles */
@media screen and (max-width: 1024px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    header p {
        font-size: 1rem;
    }
    
    .game-container {
        padding: 20px;
    }
    
    .stitches-container {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .stitch-card {
        padding: 15px;
    }
    
    .stitch-card img {
        height: 180px;
    }
    
    .difficulty-btn {
        min-width: 130px;
        margin: 5px;
        padding: 12px 15px;
    }
    
    .instruction-step {
        padding: 12px;
    }
    
    .crochet-container {
        height: 500px;
    }
    
    .pattern-instructions-container {
        padding: 15px;
    }
    
    .pattern-steps li {
        min-width: 180px;
    }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }
    
    header p {
        font-size: 0.9rem;
    }
    
    .game-container {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .stitches-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stitch-card {
        padding: 15px;
        max-width: 100%;
    }
    
    .stitch-card img {
        height: 160px;
    }
    
    .stitch-card h3 {
        font-size: 1.1rem;
    }
    
    .stitch-card p {
        font-size: 0.9rem;
    }
    
    .difficulty-selector {
        margin-bottom: 20px;
    }
    
    .difficulty-btn {
        display: block;
        width: 100%;
        max-width: 300px;
        margin: 10px auto;
        padding: 15px;
        min-height: 70px;
    }
    
    .btn-icon {
        font-size: 1.8rem;
    }
    
    .instruction-step {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }
    
    .step-number {
        margin-bottom: 10px;
        margin-right: 0;
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
    
    .step-text {
        font-size: 0.95rem;
    }
    
    .score-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .score-box {
        margin: 5px 0;
        padding: 12px;
    }
    
    .score-box h3 {
        font-size: 0.9rem;
    }
    
    .score-box p {
        font-size: 1.3rem;
    }
    
    .crochet-container {
        height: 450px;
    }
    
    .stitch-grid {
        grid-template-columns: repeat(5, 40px);
        grid-template-rows: repeat(5, 40px);
        gap: 6px;
    }
    
    .stitch-cell {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }
    
    .yarn-container {
        gap: 10px;
        padding: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .yarn-ball {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }
    
    .yarn-ball::before {
        font-size: 1.2rem;
    }
    
    .hook {
        width: 70px;
        height: 120px;
    }
    
    .hook::before {
        font-size: 1.5rem;
    }
    
    .pattern-instructions-container {
        padding: 12px;
    }
    
    .pattern-title {
        font-size: 1rem;
    }
    
    .pattern-steps {
        flex-direction: column;
    }
    
    .pattern-steps li {
        font-size: 0.85rem;
        min-width: 100%;
    }
    
    .view-controls {
        gap: 5px;
    }
    
    .view-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
    }
    
    .controls-container {
        gap: 5px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        margin: 5px 2px;
    }
    
    .current-step-box {
        padding: 15px;
    }
    
    .current-step-box h3 {
        font-size: 1.1rem;
    }
    
    .current-step-box p {
        font-size: 1rem;
    }
    
    .game-status {
        font-size: 1rem;
        padding: 15px 20px;
        max-width: 90%;
    }
    
    .help-content {
        padding: 0 10px;
    }
    
    .help-content h3 {
        font-size: 1.3rem;
    }
    
    .help-step {
        flex-direction: column;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .help-number {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .modal-content {
        padding: 20px;
        margin: 10px;
    }
    
    .modal h3 {
        font-size: 1.3rem;
    }
    
    .donation-banner {
        flex-direction: column;
        text-align: center;
    }
    
    .donation-banner .btn {
        max-width: 100%;
        margin-top: 15px;
    }
}

/* Extra Small Mobile */
@media screen and (max-width: 480px) {
    .container {
        padding: 8px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    header p {
        font-size: 0.85rem;
    }
    
    .game-container {
        padding: 12px;
    }
    
    .stitch-card img {
        height: 140px;
    }
    
    .difficulty-btn {
        min-height: 60px;
        padding: 12px;
    }
    
    .btn-icon {
        font-size: 1.5rem;
    }
    
    .btn-text {
        font-size: 0.9rem;
    }
    
    .btn-text small {
        font-size: 0.75rem;
    }
    
    .crochet-container {
        height: 400px;
    }
    
    .stitch-grid {
        grid-template-columns: repeat(4, 35px);
        grid-template-rows: repeat(4, 35px);
        gap: 5px;
    }
    
    .stitch-cell {
        width: 35px;
        height: 35px;
        min-width: 35px;
        min-height: 35px;
    }
    
    .yarn-ball {
        width: 40px;
        height: 40px;
        min-width: 40px;
    }
    
    .yarn-ball::before {
        font-size: 1rem;
    }
    
    .hook {
        width: 60px;
        height: 100px;
    }
    
    .hook::before {
        font-size: 1.2rem;
    }
    
    .pattern-instructions-container {
        padding: 10px;
    }
    
    .pattern-title {
        font-size: 0.9rem;
    }
    
    .pattern-steps li {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .step-text {
        font-size: 0.9rem;
    }
    
    .score-box h3 {
        font-size: 0.85rem;
    }
    
    .score-box p {
        font-size: 1.2rem;
    }
    
    .current-step-box h3 {
        font-size: 1rem;
    }
    
    .current-step-box p {
        font-size: 0.9rem;
    }
}