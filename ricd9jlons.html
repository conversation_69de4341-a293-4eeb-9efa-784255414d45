
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Crochet Mastery: A Complete Guide</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { font-family: 'Inter', sans-serif; }
    /* For Flashcard flip effect - simple version without 3D transform for broader compatibility and Tailwind purity */
    .flashcard-front, .flashcard-back {
      backface-visibility: hidden; /* Useful if we were doing 3D transforms */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-pink-50 text-gray-800">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
    